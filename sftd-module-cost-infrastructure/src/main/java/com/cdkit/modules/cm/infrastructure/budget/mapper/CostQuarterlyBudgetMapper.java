package com.cdkit.modules.cm.infrastructure.budget.mapper;

import java.util.List;
import java.math.BigDecimal;

import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudget;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 季度预算主表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
public interface CostQuarterlyBudgetMapper extends BaseMapper<CostQuarterlyBudget> {

    /**
     * 汇总与指定年度预算相关的已审批通过的季度预算的项目预算总额
     * 用于计算年度收入剩余预算金额
     *
     * @param annualBudgetId 年度预算ID
     * @param excludeQuarterlyBudgetId 排除的季度预算ID（可为null）
     * @return 已审批季度预算的项目预算总额
     */
    BigDecimal sumApprovedQuarterlyBudgetRevenueByAnnualBudgetId(
            @Param("annualBudgetId") String annualBudgetId,
            @Param("excludeQuarterlyBudgetId") String excludeQuarterlyBudgetId);

    /**
     * 汇总与指定年度预算和预算科目相关的已审批通过的季度预算的支出金额
     * 用于计算年度剩余支出预算金额
     *
     * @param annualBudgetId 年度预算ID
     * @param budgetSubjectCode 预算科目编码
     * @param excludeQuarterlyBudgetId 排除的季度预算ID（可为null）
     * @return 已审批季度预算的支出金额
     */
    BigDecimal sumApprovedQuarterlyBudgetExpenditureBySubjectCode(
            @Param("annualBudgetId") String annualBudgetId,
            @Param("budgetSubjectCode") String budgetSubjectCode,
            @Param("excludeQuarterlyBudgetId") String excludeQuarterlyBudgetId);

}
