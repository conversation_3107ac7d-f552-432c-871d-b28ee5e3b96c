package com.cdkit.modules.cm.infrastructure.budget.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCenterIndirectCostEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetCenterIndirectCostRepository;
import com.cdkit.modules.cm.infrastructure.budget.converter.CostQuarterlyBudgetCenterIndirectCostConverter;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetCenterIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetCenterIndirectCostMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 季度预算-预算科目明细本中心间接成本仓储实现
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CostQuarterlyBudgetCenterIndirectCostRepositoryImpl implements CostQuarterlyBudgetCenterIndirectCostRepository {

    private final CostQuarterlyBudgetCenterIndirectCostMapper mapper;
    private final CostQuarterlyBudgetCenterIndirectCostConverter converter;

    @Override
    public CostQuarterlyBudgetCenterIndirectCostEntity save(CostQuarterlyBudgetCenterIndirectCostEntity entity) {
        CostQuarterlyBudgetCenterIndirectCost infraEntity = converter.toInfrastructure(entity);
        mapper.insert(infraEntity);
        return converter.toDomain(infraEntity);
    }

    @Override
    public int saveBatch(List<CostQuarterlyBudgetCenterIndirectCostEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        List<CostQuarterlyBudgetCenterIndirectCost> infraEntities = entities.stream()
                .map(converter::toInfrastructure)
                .collect(Collectors.toList());

        // 使用MyBatis Plus的批量插入
        for (CostQuarterlyBudgetCenterIndirectCost infraEntity : infraEntities) {
            mapper.insert(infraEntity);
        }

        log.info("批量保存本中心间接成本完成，保存数量: {}", entities.size());
        return entities.size();
    }

    @Override
    public CostQuarterlyBudgetCenterIndirectCostEntity updateById(CostQuarterlyBudgetCenterIndirectCostEntity entity) {
        CostQuarterlyBudgetCenterIndirectCost infraEntity = converter.toInfrastructure(entity);
        mapper.updateById(infraEntity);
        return converter.toDomain(infraEntity);
    }

    @Override
    public void deleteById(String id) {
        mapper.deleteById(id);
    }

    @Override
    public void deleteByIds(List<String> ids) {
        mapper.deleteBatchIds(ids);
    }

    @Override
    public int deleteByQuarterlyBudgetId(String quarterlyBudgetId) {
        LambdaUpdateWrapper<CostQuarterlyBudgetCenterIndirectCost> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CostQuarterlyBudgetCenterIndirectCost::getQuarterlyBudgetId, quarterlyBudgetId);
        
        int deletedCount = mapper.delete(updateWrapper);
        log.info("根据季度预算ID删除本中心间接成本完成，季度预算ID: {}, 删除数量: {}", quarterlyBudgetId, deletedCount);
        return deletedCount;
    }

    @Override
    public CostQuarterlyBudgetCenterIndirectCostEntity findById(String id) {
        CostQuarterlyBudgetCenterIndirectCost infraEntity = mapper.selectById(id);
        return infraEntity != null ? converter.toDomain(infraEntity) : null;
    }

    @Override
    public List<CostQuarterlyBudgetCenterIndirectCostEntity> findByIds(List<String> ids) {
        List<CostQuarterlyBudgetCenterIndirectCost> infraEntities = mapper.selectBatchIds(ids);
        return infraEntities.stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<CostQuarterlyBudgetCenterIndirectCostEntity> findByQuarterlyBudgetId(String quarterlyBudgetId) {
        LambdaQueryWrapper<CostQuarterlyBudgetCenterIndirectCost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostQuarterlyBudgetCenterIndirectCost::getQuarterlyBudgetId, quarterlyBudgetId);
        
        List<CostQuarterlyBudgetCenterIndirectCost> infraEntities = mapper.selectList(queryWrapper);
        return infraEntities.stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public CostQuarterlyBudgetCenterIndirectCostEntity findByQuarterlyBudgetIdAndSubjectCode(String quarterlyBudgetId, String budgetSubjectCode) {
        LambdaQueryWrapper<CostQuarterlyBudgetCenterIndirectCost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostQuarterlyBudgetCenterIndirectCost::getQuarterlyBudgetId, quarterlyBudgetId)
                   .eq(CostQuarterlyBudgetCenterIndirectCost::getBudgetSubjectCode, budgetSubjectCode);
        
        CostQuarterlyBudgetCenterIndirectCost infraEntity = mapper.selectOne(queryWrapper);
        return infraEntity != null ? converter.toDomain(infraEntity) : null;
    }

    @Override
    public BigDecimal sumExpenditureBudgetAmountByQuarterlyBudgetId(String quarterlyBudgetId) {
        return mapper.sumExpenditureBudgetAmountByQuarterlyBudgetId(quarterlyBudgetId);
    }
}
