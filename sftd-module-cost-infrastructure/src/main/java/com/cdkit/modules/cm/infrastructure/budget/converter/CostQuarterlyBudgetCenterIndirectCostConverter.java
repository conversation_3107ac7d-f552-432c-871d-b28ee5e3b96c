package com.cdkit.modules.cm.infrastructure.budget.converter;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCenterIndirectCostEntity;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetCenterIndirectCost;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 季度预算-预算科目明细本中心间接成本转换器
 * 负责领域实体和基础设施实体之间的转换
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Component
public class CostQuarterlyBudgetCenterIndirectCostConverter {

    /**
     * 领域实体转换为基础设施实体
     *
     * @param domainEntity 领域实体
     * @return 基础设施实体
     */
    public CostQuarterlyBudgetCenterIndirectCost toInfrastructure(CostQuarterlyBudgetCenterIndirectCostEntity domainEntity) {
        if (domainEntity == null) {
            return null;
        }

        CostQuarterlyBudgetCenterIndirectCost infraEntity = new CostQuarterlyBudgetCenterIndirectCost();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    /**
     * 基础设施实体转换为领域实体
     *
     * @param infraEntity 基础设施实体
     * @return 领域实体
     */
    public CostQuarterlyBudgetCenterIndirectCostEntity toDomain(CostQuarterlyBudgetCenterIndirectCost infraEntity) {
        if (infraEntity == null) {
            return null;
        }

        CostQuarterlyBudgetCenterIndirectCostEntity domainEntity = new CostQuarterlyBudgetCenterIndirectCostEntity();
        BeanUtils.copyProperties(infraEntity, domainEntity);
        return domainEntity;
    }
}
