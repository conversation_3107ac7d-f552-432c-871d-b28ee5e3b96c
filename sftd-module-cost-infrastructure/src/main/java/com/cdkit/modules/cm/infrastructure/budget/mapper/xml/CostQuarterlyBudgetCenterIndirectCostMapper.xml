<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetCenterIndirectCostMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		update cost_quarterly_budget_center_indirect_cost set del_flag=1
		WHERE
			 quarterly_budget_id = #{mainId} AND del_flag = 0</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetCenterIndirectCost">
		SELECT *
		FROM  cost_quarterly_budget_center_indirect_cost
		WHERE
			 quarterly_budget_id = #{mainId} AND del_flag = 0	</select>

	<select id="sumExpenditureBudgetAmountByQuarterlyBudgetId" parameterType="java.lang.String" resultType="java.math.BigDecimal">
		SELECT COALESCE(SUM(expenditure_budget_amount), 0)
		FROM cost_quarterly_budget_center_indirect_cost
		WHERE quarterly_budget_id = #{quarterlyBudgetId}
		  AND del_flag = 0
	</select>
</mapper>
