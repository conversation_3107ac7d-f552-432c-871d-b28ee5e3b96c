package com.cdkit.modules.cm.infrastructure.businessdata.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.cm.domain.businessdata.model.entity.CostBudgetSubjectEntity;
import com.cdkit.modules.cm.domain.businessdata.repository.CostBudgetSubjectRepository;
import com.cdkit.modules.cm.infrastructure.businessdata.entity.CostBudgetSubject;
import com.cdkit.modules.cm.infrastructure.businessdata.mapper.CostBudgetSubjectMapper;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.common.page.OrderParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 预算科目管理仓储实现
 * <AUTHOR>
 * @date 2025-07-31
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CostBudgetSubjectRepositoryImpl implements CostBudgetSubjectRepository {

    private final CostBudgetSubjectMapper costBudgetSubjectMapper;

    @Override
    public CostBudgetSubjectEntity save(CostBudgetSubjectEntity entity) {
        CostBudgetSubject po = convertToPO(entity);
        costBudgetSubjectMapper.insert(po);
        entity.setId(po.getId());
        return entity;
    }

    @Override
    public CostBudgetSubjectEntity updateById(CostBudgetSubjectEntity entity) {
        CostBudgetSubject po = convertToPO(entity);
        costBudgetSubjectMapper.updateById(po);
        return entity;
    }

    @Override
    public void deleteById(String id) {
        costBudgetSubjectMapper.deleteById(id);
    }

    @Override
    public void deleteBatchByIds(List<String> ids) {
        costBudgetSubjectMapper.deleteBatchIds(ids);
    }

    @Override
    public CostBudgetSubjectEntity findById(String id) {
        CostBudgetSubject po = costBudgetSubjectMapper.selectById(id);
        return po != null ? convertToEntity(po) : null;
    }

    @Override
    public CostBudgetSubjectEntity findBySubjectCode(String subjectCode) {
        LambdaQueryWrapper<CostBudgetSubject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostBudgetSubject::getSubjectCode, subjectCode);
        CostBudgetSubject po = costBudgetSubjectMapper.selectOne(queryWrapper);
        return po != null ? convertToEntity(po) : null;
    }

    @Override
    public List<CostBudgetSubjectEntity> findAllEnabled() {
        LambdaQueryWrapper<CostBudgetSubject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostBudgetSubject::getSubjectStatus, "Y");
        queryWrapper.orderByAsc(CostBudgetSubject::getSortOrder);
        queryWrapper.orderByDesc(CostBudgetSubject::getCreateTime);
        List<CostBudgetSubject> poList = costBudgetSubjectMapper.selectList(queryWrapper);
        return poList.stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public List<CostBudgetSubjectEntity> findByStatus(String subjectStatus) {
        LambdaQueryWrapper<CostBudgetSubject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostBudgetSubject::getSubjectStatus, subjectStatus);
        queryWrapper.orderByAsc(CostBudgetSubject::getSortOrder);
        queryWrapper.orderByDesc(CostBudgetSubject::getCreateTime);
        List<CostBudgetSubject> poList = costBudgetSubjectMapper.selectList(queryWrapper);
        return poList.stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public PageRes<CostBudgetSubjectEntity> queryPageList(CostBudgetSubjectEntity queryEntity, PageReq pageReq) {
        // 构建查询条件
        LambdaQueryWrapper<CostBudgetSubject> queryWrapper = new LambdaQueryWrapper<>();

        if (queryEntity != null) {
            if (StringUtils.hasText(queryEntity.getSubjectCode())) {
                queryWrapper.like(CostBudgetSubject::getSubjectCode, queryEntity.getSubjectCode());
            }
            if (StringUtils.hasText(queryEntity.getSubjectName())) {
                queryWrapper.like(CostBudgetSubject::getSubjectName, queryEntity.getSubjectName());
            }
            if (StringUtils.hasText(queryEntity.getSubjectStatus())) {
                queryWrapper.eq(CostBudgetSubject::getSubjectStatus, queryEntity.getSubjectStatus());
            }
            if (queryEntity.getTenantId() != null) {
                queryWrapper.eq(CostBudgetSubject::getTenantId, queryEntity.getTenantId());
            }
        }

        // 构建分页对象
        Page<CostBudgetSubject> page = new Page<>(pageReq.getCurrent(), pageReq.getSize());

        // 处理排序
        if (pageReq.getOrderParam() != null && !pageReq.getOrderParam().isEmpty()) {
            for (OrderParam orderParam : pageReq.getOrderParam()) {
                String field = orderParam.getField();
                if ("asc".equalsIgnoreCase(orderParam.getOrder())) {
                    if ("sort_order".equals(field)) {
                        queryWrapper.orderByAsc(CostBudgetSubject::getSortOrder);
                    } else if ("create_time".equals(field)) {
                        queryWrapper.orderByAsc(CostBudgetSubject::getCreateTime);
                    } else if ("update_time".equals(field)) {
                        queryWrapper.orderByAsc(CostBudgetSubject::getUpdateTime);
                    } else if ("subject_code".equals(field)) {
                        queryWrapper.orderByAsc(CostBudgetSubject::getSubjectCode);
                    } else if ("subject_name".equals(field)) {
                        queryWrapper.orderByAsc(CostBudgetSubject::getSubjectName);
                    }
                } else {
                    if ("sort_order".equals(field)) {
                        queryWrapper.orderByDesc(CostBudgetSubject::getSortOrder);
                    } else if ("create_time".equals(field)) {
                        queryWrapper.orderByDesc(CostBudgetSubject::getCreateTime);
                    } else if ("update_time".equals(field)) {
                        queryWrapper.orderByDesc(CostBudgetSubject::getUpdateTime);
                    } else if ("subject_code".equals(field)) {
                        queryWrapper.orderByDesc(CostBudgetSubject::getSubjectCode);
                    } else if ("subject_name".equals(field)) {
                        queryWrapper.orderByDesc(CostBudgetSubject::getSubjectName);
                    }
                }
            }
        }

        // 执行分页查询
        IPage<CostBudgetSubject> pageResult = costBudgetSubjectMapper.selectPage(page, queryWrapper);

        // 转换结果
        List<CostBudgetSubjectEntity> entityList = pageResult.getRecords().stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());

        return PageRes.of(pageReq.getCurrent(), pageReq.getSize(), entityList, pageResult.getTotal(), pageResult.getPages());
    }



    @Override
    public boolean existsBySubjectCodeAndIdNot(String subjectCode, String excludeId) {
        LambdaQueryWrapper<CostBudgetSubject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostBudgetSubject::getSubjectCode, subjectCode);
        queryWrapper.ne(CostBudgetSubject::getId, excludeId);
        return costBudgetSubjectMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public List<CostBudgetSubjectEntity> findBySubjectNameLike(String subjectName) {
        LambdaQueryWrapper<CostBudgetSubject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(CostBudgetSubject::getSubjectName, subjectName);
        queryWrapper.orderByAsc(CostBudgetSubject::getSortOrder);
        queryWrapper.orderByDesc(CostBudgetSubject::getCreateTime);
        List<CostBudgetSubject> poList = costBudgetSubjectMapper.selectList(queryWrapper);
        return poList.stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public List<CostBudgetSubjectEntity> findRawMaterialAndMainMaterialSubjects() {
        log.info("开始查询原材料和主要原料相关的预算科目");

        try {
            LambdaQueryWrapper<CostBudgetSubject> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CostBudgetSubject::getSubjectStatus, "Y") // 只查询启用状态的科目
                   .and(w -> w.like(CostBudgetSubject::getSubjectName, "原材料")
                            .or()
                            .like(CostBudgetSubject::getSubjectName, "主要原料")
                            .or()
                            .like(CostBudgetSubject::getSubjectName, "原料")
                            .or()
                            .like(CostBudgetSubject::getSubjectName, "材料"))
                   .orderByAsc(CostBudgetSubject::getSortOrder)
                   .orderByDesc(CostBudgetSubject::getCreateTime);

            List<CostBudgetSubject> poList = costBudgetSubjectMapper.selectList(wrapper);
            List<CostBudgetSubjectEntity> entityList = poList.stream()
                    .map(this::convertToEntity)
                    .collect(Collectors.toList());

            log.info("查询原材料和主要原料相关的预算科目成功，共查询到{}条记录", entityList.size());
            return entityList;

        } catch (Exception e) {
            log.error("查询原材料和主要原料相关的预算科目失败", e);
            throw new RuntimeException("查询原材料和主要原料预算科目失败：" + e.getMessage());
        }
    }

    /**
     * 领域实体转换为持久化对象
     */
    private CostBudgetSubject convertToPO(CostBudgetSubjectEntity entity) {
        CostBudgetSubject po = new CostBudgetSubject();
        BeanUtils.copyProperties(entity, po);
        return po;
    }

    /**
     * 持久化对象转换为领域实体
     */
    private CostBudgetSubjectEntity convertToEntity(CostBudgetSubject po) {
        CostBudgetSubjectEntity entity = new CostBudgetSubjectEntity();
        BeanUtils.copyProperties(po, entity);
        return entity;
    }
}
