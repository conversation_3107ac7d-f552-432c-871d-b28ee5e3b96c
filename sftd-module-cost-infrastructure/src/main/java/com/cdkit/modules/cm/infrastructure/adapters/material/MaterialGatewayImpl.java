package com.cdkit.modules.cm.infrastructure.adapters.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.cm.domain.gateway.material.MaterialExternalGateway;
import com.cdkit.modules.cm.domain.gateway.material.MaterialGateway;
import com.cdkit.modules.cm.domain.gateway.material.entity.MaterialDetailDTO;
import com.cdkit.modules.cm.domain.gateway.material.entity.MaterialDetailEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 物料网关实现
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MaterialGatewayImpl implements MaterialGateway {

    private final MaterialExternalGateway materialExternalGateway;

    @Override
    public MaterialDetailDTO getMaterialByCode(String materialCode) {
        if (!StringUtils.hasText(materialCode)) {
            return null;
        }

        try {
            log.info("查询物料信息，materialCode: {}", materialCode);

            // 调用外部网关查询物料信息
            Page<MaterialDetailEntity> materialPage = materialExternalGateway.listByPage(materialCode, null, 1, 1);
            
            if (materialPage == null || materialPage.getRecords() == null || materialPage.getRecords().isEmpty()) {
                log.warn("未找到物料信息，materialCode: {}", materialCode);
                return null;
            }

            // 取第一条记录
            MaterialDetailEntity materialEntity = materialPage.getRecords().get(0);
            
            // 转换为DTO
            MaterialDetailDTO materialDTO = new MaterialDetailDTO();
            materialDTO.setMaterialCode(materialEntity.getMaterialCode());
            materialDTO.setMaterialName(materialEntity.getMaterialName());
            materialDTO.setMaterialType(materialEntity.getMaterialType());
            // 将basicUnitId映射为unit
            materialDTO.setUnit(materialEntity.getBasicUnitId());
            materialDTO.setRemark(materialEntity.getRemark());

            log.info("查询物料信息成功，materialCode: {}, materialName: {}, unit: {}", 
                    materialCode, materialDTO.getMaterialName(), materialDTO.getUnit());

            return materialDTO;

        } catch (Exception e) {
            log.error("查询物料信息失败，materialCode: {}", materialCode, e);
            return null;
        }
    }
}
