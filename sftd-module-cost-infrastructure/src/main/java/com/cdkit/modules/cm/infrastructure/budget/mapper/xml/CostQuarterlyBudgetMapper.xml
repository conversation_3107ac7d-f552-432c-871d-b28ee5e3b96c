<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetMapper">

    <!-- 汇总与指定年度预算相关的已锁定季度预算的项目预算总额 -->
    <!-- 注意：只统计已锁定的季度预算，因为这些是已确定不会变更的数据 -->
    <!-- 通过季度计划编号关联获取项目编号 -->
    <select id="sumApprovedQuarterlyBudgetRevenueByAnnualBudgetId" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(qb.revenue_budget_amount), 0)
        FROM cost_quarterly_budget qb
        INNER JOIN cost_project_plan cpp ON qb.quarterly_plan_no = cpp.plan_code
        INNER JOIN cost_annual_budget_detail abd ON cpp.project_code = abd.project_code
        INNER JOIN cost_annual_budget ab ON abd.budget_id = ab.id
        WHERE abd.budget_id = #{annualBudgetId}
          AND qb.budget_status = 'LOCKED'
          AND ab.budget_status = 'LOCKED'
          AND qb.del_flag = 0
          AND cpp.del_flag = 0
          AND abd.del_flag = 0
          AND ab.del_flag = 0
          <if test="excludeQuarterlyBudgetId != null and excludeQuarterlyBudgetId != ''">
              AND qb.id != #{excludeQuarterlyBudgetId}
          </if>
    </select>

    <!-- 汇总与指定年度预算和预算科目相关的已锁定季度预算的支出金额 -->
    <!-- 注意：只统计已锁定的季度预算，因为这些是已确定不会变更的数据 -->
    <!-- 通过季度计划编号关联获取项目编号 -->
    <select id="sumApprovedQuarterlyBudgetExpenditureBySubjectCode" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(sdc.expenditure_budget_amount), 0)
        FROM cost_quarterly_budget qb
        INNER JOIN cost_project_plan cpp ON qb.quarterly_plan_no = cpp.plan_code
        INNER JOIN cost_annual_budget_detail abd ON cpp.project_code = abd.project_code
        INNER JOIN cost_annual_budget ab ON abd.budget_id = ab.id
        INNER JOIN cost_quarterly_budget_subject_direct_cost sdc ON qb.id = sdc.quarterly_budget_id
        WHERE abd.budget_id = #{annualBudgetId}
          AND qb.budget_status = 'LOCKED'
          AND ab.budget_status = 'LOCKED'
          AND qb.del_flag = 0
          AND cpp.del_flag = 0
          AND abd.del_flag = 0
          AND ab.del_flag = 0
          AND sdc.del_flag = 0
          AND sdc.budget_subject_code = #{budgetSubjectCode}
          <if test="excludeQuarterlyBudgetId != null and excludeQuarterlyBudgetId != ''">
              AND qb.id != #{excludeQuarterlyBudgetId}
          </if>
    </select>

</mapper>