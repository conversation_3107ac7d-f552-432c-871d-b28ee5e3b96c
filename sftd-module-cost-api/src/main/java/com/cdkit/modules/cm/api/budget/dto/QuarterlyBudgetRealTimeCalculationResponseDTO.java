package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 季度预算实时计算响应DTO
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Schema(description = "季度预算实时计算响应DTO")
@Data
public class QuarterlyBudgetRealTimeCalculationResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**年度收入剩余预算金额（不含税，元）*/
    @Schema(description = "年度收入剩余预算金额（不含税，元）")
    private BigDecimal annualRevenueRemainingBudget;

    /**年度支出剩余预算金额（不含税，元）*/
    @Schema(description = "年度支出剩余预算金额（不含税，元）")
    private BigDecimal annualExpenditureRemainingBudget;

    /**项目支出预算总额（不含税，元）*/
    @Schema(description = "项目支出预算总额（不含税，元）")
    private BigDecimal projectExpenditureBudgetTotal;

    /**间接费预算总额（不含税，元）*/
    @Schema(description = "间接费预算总额（不含税，元）")
    private BigDecimal indirectCostBudgetTotal;

    /**项目边际利润（元）*/
    @Schema(description = "项目边际利润（元）")
    private BigDecimal projectMarginalProfit;

    /**项目边际利润率*/
    @Schema(description = "项目边际利润率")
    private BigDecimal projectMarginalProfitRate;

    /**项目净利润（元）*/
    @Schema(description = "项目净利润（元）")
    private BigDecimal projectNetProfit;

    /**项目净利润率*/
    @Schema(description = "项目净利润率")
    private BigDecimal projectNetProfitRate;

    /**预算科目明细直接成本列表*/
    @Schema(description = "预算科目明细直接成本列表")
    private List<SubjectDirectCostDTO> subjectDirectCostList;

    /**本中心间接成本列表*/
    @Schema(description = "本中心间接成本列表")
    private List<CenterIndirectCostDTO> centerIndirectCostList;

    /**非经营中心间接成本列表*/
    @Schema(description = "非经营中心间接成本列表")
    private List<NonOperatingIndirectCostDTO> nonOperatingIndirectCostList;

    /**综合管理间接成本列表*/
    @Schema(description = "综合管理间接成本列表")
    private List<ComprehensiveIndirectCostDTO> comprehensiveIndirectCostList;

    /**
     * 预算科目明细直接成本DTO
     */
    @Schema(description = "预算科目明细直接成本DTO")
    @Data
    public static class SubjectDirectCostDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**直接成本-预算科目编码（关联预算科目表）*/
        @Schema(description = "直接成本-预算科目编码（关联预算科目表）")
        private String budgetSubjectCode;

        /**直接成本-预算科目名称*/
        @Schema(description = "直接成本-预算科目名称")
        private String budgetSubjectName;

        /**直接成本-科目释义*/
        @Schema(description = "直接成本-科目释义")
        private String subjectDescription;

        /**年度支出预算金额（不含税，元）*/
        @Schema(description = "年度支出预算金额（不含税，元）")
        private BigDecimal annualExpenditureBudgetAmount;

        /**年度剩余支出预算金额（不含税，元）*/
        @Schema(description = "年度剩余支出预算金额（不含税，元）")
        private BigDecimal annualRemainExpendBudget;

        /**间接费预算参考金额（不含税，元）*/
        @Schema(description = "间接费预算参考金额（不含税，元）")
        private BigDecimal indirectCostReferenceAmount;

        /**间接费预算金额（不含税，元）*/
        @Schema(description = "间接费预算金额（不含税，元）")
        private BigDecimal indirectCostBudgetAmount;

        /**支出预算金额（不含税，元）*/
        @Schema(description = "支出预算金额（不含税，元）")
        private BigDecimal expenditureBudgetAmount;
    }

    /**
     * 本中心间接成本DTO
     */
    @Schema(description = "本中心间接成本DTO")
    @Data
    public static class CenterIndirectCostDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**本中心间接成本-预算科目编码（关联预算科目表）*/
        @Schema(description = "本中心间接成本-预算科目编码（关联预算科目表）")
        private String budgetSubjectCode;

        /**本中心间接成本-预算科目名称*/
        @Schema(description = "本中心间接成本-预算科目名称")
        private String budgetSubjectName;

        /**本中心间接成本-预算释义*/
        @Schema(description = "本中心间接成本-预算释义")
        private String subjectDescription;

        /**本中心间接成本-支出预算金额（元）*/
        @Schema(description = "本中心间接成本-支出预算金额（元）")
        private BigDecimal expenditureBudgetAmount;
    }

    /**
     * 非经营中心间接成本DTO
     */
    @Schema(description = "非经营中心间接成本DTO")
    @Data
    public static class NonOperatingIndirectCostDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**非经营中心间接成本-预算科目编码（关联预算科目表）*/
        @Schema(description = "非经营中心间接成本-预算科目编码（关联预算科目表）")
        private String budgetSubjectCode;

        /**非经营中心间接成本-预算科目名称*/
        @Schema(description = "非经营中心间接成本-预算科目名称")
        private String budgetSubjectName;

        /**非经营中心间接成本-预算释义*/
        @Schema(description = "非经营中心间接成本-预算释义")
        private String subjectDescription;

        /**非经营中心间接成本-支出预算金额（元）*/
        @Schema(description = "非经营中心间接成本-支出预算金额（元）")
        private BigDecimal expenditureBudgetAmount;
    }

    /**
     * 综合管理间接成本DTO
     */
    @Schema(description = "综合管理间接成本DTO")
    @Data
    public static class ComprehensiveIndirectCostDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**综合管理间接成本-预算科目编码（关联预算科目表）*/
        @Schema(description = "综合管理间接成本-预算科目编码（关联预算科目表）")
        private String budgetSubjectCode;

        /**综合管理间接成本-预算科目名称*/
        @Schema(description = "综合管理间接成本-预算科目名称")
        private String budgetSubjectName;

        /**综合管理间接成本-预算释义*/
        @Schema(description = "综合管理间接成本-预算释义")
        private String subjectDescription;

        /**综合管理间接成本-支出预算金额（元）*/
        @Schema(description = "综合管理间接成本-支出预算金额（元）")
        private BigDecimal expenditureBudgetAmount;
    }
}
