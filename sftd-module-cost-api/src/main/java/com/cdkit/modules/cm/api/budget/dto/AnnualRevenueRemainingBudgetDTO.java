package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 年度收入剩余预算金额响应DTO
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Schema(description = "年度收入剩余预算金额响应DTO")
@Data
public class AnnualRevenueRemainingBudgetDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**年度收入剩余预算金额（元）*/
    @Schema(description = "年度收入剩余预算金额（元）")
    private BigDecimal annualRevenueRemainingBudgetAmount;

    /**年度预算收入金额（元）*/
    @Schema(description = "年度预算收入金额（元）")
    private BigDecimal annualRevenueBudgetAmount;

    /**已审批季度预算总额（元）*/
    @Schema(description = "已审批季度预算总额（元）")
    private BigDecimal approvedQuarterlyBudgetTotal;

    /**年度预算编码*/
    @Schema(description = "年度预算编码")
    private String annualBudgetCode;

    /**年度预算名称*/
    @Schema(description = "年度预算名称")
    private String annualBudgetName;
}
