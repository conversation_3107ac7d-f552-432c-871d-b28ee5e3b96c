package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 季度预算间接成本导入响应DTO
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
@Schema(description = "季度预算间接成本导入响应DTO")
public class QuarterlyBudgetIndirectCostImportResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**导入成功的数据列表*/
    @Schema(description = "导入成功的数据列表")
    private List<ImportSuccessItemDTO> successList;

    /**导入失败的数据列表*/
    @Schema(description = "导入失败的数据列表")
    private List<ImportFailureItemDTO> failureList;

    /**重新计算后的季度预算汇总信息*/
    @Schema(description = "重新计算后的季度预算汇总信息")
    private QuarterlyBudgetSummaryDTO budgetSummary;

    /**
     * 导入成功项DTO
     */
    @Data
    @Schema(description = "导入成功项DTO")
    public static class ImportSuccessItemDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**预算科目编码*/
        @Schema(description = "预算科目编码")
        private String budgetSubjectCode;

        /**预算科目名称*/
        @Schema(description = "预算科目名称")
        private String budgetSubjectName;

        /**科目释义*/
        @Schema(description = "科目释义")
        private String subjectDescription;

        /**支出预算金额（元）*/
        @Schema(description = "支出预算金额（元）")
        private BigDecimal expenditureBudgetAmount;

        /**操作类型（新增/更新）*/
        @Schema(description = "操作类型（新增/更新）")
        private String operationType;
    }

    /**
     * 导入失败项DTO
     */
    @Data
    @Schema(description = "导入失败项DTO")
    public static class ImportFailureItemDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**行号*/
        @Schema(description = "行号")
        private Integer rowNumber;

        /**预算科目编码*/
        @Schema(description = "预算科目编码")
        private String budgetSubjectCode;

        /**预算科目名称*/
        @Schema(description = "预算科目名称")
        private String budgetSubjectName;

        /**支出预算金额（元）*/
        @Schema(description = "支出预算金额（元）")
        private String expenditureBudgetAmount;

        /**错误原因*/
        @Schema(description = "错误原因")
        private String errorMessage;
    }

    /**
     * 季度预算汇总信息DTO
     */
    @Data
    @Schema(description = "季度预算汇总信息DTO")
    public static class QuarterlyBudgetSummaryDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**项目支出预算总额（不含税，元）*/
        @Schema(description = "项目支出预算总额（不含税，元）")
        private BigDecimal projectExpenditureBudgetTotal;

        /**间接费预算总额（不含税，元）*/
        @Schema(description = "间接费预算总额（不含税，元）")
        private BigDecimal indirectCostBudgetTotal;

        /**项目边际利润（元）*/
        @Schema(description = "项目边际利润（元）")
        private BigDecimal projectMarginalProfit;

        /**项目边际利润率*/
        @Schema(description = "项目边际利润率")
        private BigDecimal projectMarginalProfitRate;

        /**项目净利润（元）*/
        @Schema(description = "项目净利润（元）")
        private BigDecimal projectNetProfit;

        /**项目净利润率*/
        @Schema(description = "项目净利润率")
        private BigDecimal projectNetProfitRate;
    }
}
