package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 季度预算提交请求DTO
 * 支持单条提交和批量提交两种场景
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@Schema(description = "季度预算提交请求DTO")
public class QuarterlyBudgetSubmitRequestDTO {

    /**
     * 季度预算ID列表
     * 单条提交时传入一个ID，批量提交时传入多个ID
     */
    @NotEmpty(message = "季度预算ID列表不能为空")
    @Schema(description = "季度预算ID列表", requiredMode = Schema.RequiredMode.REQUIRED, 
            example = "[\"1234567890\", \"0987654321\"]")
    private List<String> budgetIds;

    /**
     * 提交备注
     * 可选字段，用于记录提交时的说明信息
     */
    @Schema(description = "提交备注", example = "季度预算数据已完善，申请提交审批")
    private String submitRemark;

    /**
     * 是否强制提交
     * 当存在警告信息时，是否强制提交
     */
    @Schema(description = "是否强制提交", example = "false")
    private Boolean forceSubmit = false;

    /**
     * 获取提交的预算数量
     *
     * @return 预算数量
     */
    public int getBudgetCount() {
        return budgetIds != null ? budgetIds.size() : 0;
    }

    /**
     * 是否为单条提交
     *
     * @return true-单条提交，false-批量提交
     */
    public boolean isSingleSubmit() {
        return getBudgetCount() == 1;
    }

    /**
     * 是否为批量提交
     *
     * @return true-批量提交，false-单条提交
     */
    public boolean isBatchSubmit() {
        return getBudgetCount() > 1;
    }

    /**
     * 获取第一个预算ID（用于单条提交场景）
     *
     * @return 第一个预算ID
     */
    public String getFirstBudgetId() {
        return (budgetIds != null && !budgetIds.isEmpty()) ? budgetIds.get(0) : null;
    }
}
