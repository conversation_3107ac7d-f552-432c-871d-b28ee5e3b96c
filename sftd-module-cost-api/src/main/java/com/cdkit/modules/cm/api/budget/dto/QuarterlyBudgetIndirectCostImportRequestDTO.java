package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * 季度预算间接成本导入请求DTO
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
@Schema(description = "季度预算间接成本导入请求DTO")
public class QuarterlyBudgetIndirectCostImportRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**季度计划ID（必填）*/
    @Schema(description = "季度计划ID（必填）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String quarterlyPlanId;

    /**年度预算ID（必填）*/
    @Schema(description = "年度预算ID（必填）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String annualBudgetId;

    /**间接成本类型（必填）*/
    @Schema(description = "间接成本类型：CENTER_INDIRECT-本中心间接成本，NON_OPERATING_INDIRECT-非经营中心间接成本，COMPREHENSIVE_INDIRECT-综合管理间接成本", 
            requiredMode = Schema.RequiredMode.REQUIRED)
    private IndirectCostType indirectCostType;

    /**Excel文件（必填）*/
    @Schema(description = "Excel文件（必填）", requiredMode = Schema.RequiredMode.REQUIRED)
    private MultipartFile file;

    /**
     * 间接成本类型枚举
     */
    public enum IndirectCostType {
        /**本中心间接成本*/
        CENTER_INDIRECT("本中心间接成本"),
        /**非经营中心间接成本*/
        NON_OPERATING_INDIRECT("非经营中心间接成本"),
        /**综合管理间接成本*/
        COMPREHENSIVE_INDIRECT("综合管理间接成本");

        private final String description;

        IndirectCostType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
