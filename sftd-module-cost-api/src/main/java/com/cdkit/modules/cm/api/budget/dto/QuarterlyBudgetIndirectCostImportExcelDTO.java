package com.cdkit.modules.cm.api.budget.dto;

import com.cdkitframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 季度预算间接成本导入Excel数据模型
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
@Schema(description = "季度预算间接成本导入Excel数据模型")
public class QuarterlyBudgetIndirectCostImportExcelDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**间接费预算科目（预算科目名称，同时作为预算科目编码）*/
    @Excel(name = "间接费预算科目", width = 25)
    @Schema(description = "间接费预算科目（预算科目名称，同时作为预算科目编码）")
    private String budgetSubjectName;

    /**科目预算金额（元）*/
    @Excel(name = "科目预算金额（元）", width = 15)
    @Schema(description = "科目预算金额（元）")
    private BigDecimal expenditureBudgetAmount;

    // 以下字段为后端处理时自动填充，不在Excel中

    /**预算科目编码（与预算科目名称相同）*/
    @Schema(description = "预算科目编码（与预算科目名称相同）")
    private String budgetSubjectCode;

    /**科目释义*/
    @Schema(description = "科目释义")
    private String subjectDescription;

    /**备注*/
    @Schema(description = "备注")
    private String remark;
}
