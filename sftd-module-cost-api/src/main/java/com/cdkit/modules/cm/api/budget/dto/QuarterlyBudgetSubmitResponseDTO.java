package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 季度预算提交响应DTO
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@Schema(description = "季度预算提交响应DTO")
public class QuarterlyBudgetSubmitResponseDTO {

    /**
     * 提交成功的数量
     */
    @Schema(description = "提交成功的数量", example = "2")
    private Integer successCount;

    /**
     * 提交失败的数量
     */
    @Schema(description = "提交失败的数量", example = "0")
    private Integer failureCount;

    /**
     * 总提交数量
     */
    @Schema(description = "总提交数量", example = "2")
    private Integer totalCount;

    /**
     * 提交成功的预算ID列表
     */
    @Schema(description = "提交成功的预算ID列表")
    private List<String> successBudgetIds;

    /**
     * 提交失败的详细信息
     */
    @Schema(description = "提交失败的详细信息")
    private List<SubmitFailureDetail> failureDetails;

    /**
     * 整体提交结果消息
     */
    @Schema(description = "整体提交结果消息", example = "提交成功，共2条季度预算已进入审批流程")
    private String message;

    /**
     * 提交失败详细信息
     */
    @Data
    @Schema(description = "提交失败详细信息")
    public static class SubmitFailureDetail {

        /**
         * 预算ID
         */
        @Schema(description = "预算ID")
        private String budgetId;

        /**
         * 预算单号
         */
        @Schema(description = "预算单号")
        private String budgetNo;

        /**
         * 预算名称
         */
        @Schema(description = "预算名称")
        private String budgetName;

        /**
         * 当前状态
         */
        @Schema(description = "当前状态")
        private String currentStatus;

        /**
         * 失败原因
         */
        @Schema(description = "失败原因")
        private String failureReason;
    }

    /**
     * 构建成功响应
     *
     * @param successCount 成功数量
     * @param successBudgetIds 成功的预算ID列表
     * @return 响应DTO
     */
    public static QuarterlyBudgetSubmitResponseDTO buildSuccess(Integer successCount, List<String> successBudgetIds) {
        QuarterlyBudgetSubmitResponseDTO response = new QuarterlyBudgetSubmitResponseDTO();
        response.setSuccessCount(successCount);
        response.setFailureCount(0);
        response.setTotalCount(successCount);
        response.setSuccessBudgetIds(successBudgetIds);
        response.setMessage(String.format("提交成功，共%d条季度预算已进入审批流程", successCount));
        return response;
    }

    /**
     * 构建部分成功响应
     *
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @param successBudgetIds 成功的预算ID列表
     * @param failureDetails 失败详情列表
     * @return 响应DTO
     */
    public static QuarterlyBudgetSubmitResponseDTO buildPartialSuccess(Integer successCount, Integer failureCount,
                                                                       List<String> successBudgetIds, 
                                                                       List<SubmitFailureDetail> failureDetails) {
        QuarterlyBudgetSubmitResponseDTO response = new QuarterlyBudgetSubmitResponseDTO();
        response.setSuccessCount(successCount);
        response.setFailureCount(failureCount);
        response.setTotalCount(successCount + failureCount);
        response.setSuccessBudgetIds(successBudgetIds);
        response.setFailureDetails(failureDetails);
        response.setMessage(String.format("部分提交成功，成功%d条，失败%d条", successCount, failureCount));
        return response;
    }

    /**
     * 构建失败响应
     *
     * @param failureCount 失败数量
     * @param failureDetails 失败详情列表
     * @return 响应DTO
     */
    public static QuarterlyBudgetSubmitResponseDTO buildFailure(Integer failureCount, List<SubmitFailureDetail> failureDetails) {
        QuarterlyBudgetSubmitResponseDTO response = new QuarterlyBudgetSubmitResponseDTO();
        response.setSuccessCount(0);
        response.setFailureCount(failureCount);
        response.setTotalCount(failureCount);
        response.setFailureDetails(failureDetails);
        response.setMessage(String.format("提交失败，共%d条季度预算提交失败", failureCount));
        return response;
    }
}
