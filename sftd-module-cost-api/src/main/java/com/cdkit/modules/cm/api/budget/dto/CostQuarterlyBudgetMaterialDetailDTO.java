package com.cdkit.modules.cm.api.budget.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 季度预算原材料明细DTO
 * @Author: cdkit-boot
 * @Date: 2025-08-18
 * @Version: V1.0
 */
@Schema(description = "季度预算原材料明细DTO")
@Data
public class CostQuarterlyBudgetMaterialDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**关联季度预算主表ID*/
    @Schema(description = "关联季度预算主表ID")
    private String quarterlyBudgetId;

    /**物料编码*/
    @Schema(description = "物料编码")
    private String materialCode;

    /**物料名称*/
    @Schema(description = "物料名称")
    private String materialName;

    /**用量*/
    @Schema(description = "用量")
    private BigDecimal usageAmount;

    /**单位*/
    @Schema(description = "单位")
    private String unit;

    /**不含税单价(元)*/
    @Schema(description = "不含税单价(元)")
    private BigDecimal unitPriceExcludingTax;

    /**不含税总价(元)*/
    @Schema(description = "不含税总价(元)")
    private BigDecimal totalPriceExcludingTax;

    /**编制依据*/
    @Schema(description = "编制依据")
    private String compilationBasis;

    /**备注*/
    @Schema(description = "备注")
    private String remark;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;

    /**租户ID*/
    @Schema(description = "租户ID")
    private Integer tenantId;

    /**删除标识 0:未删除 1:删除*/
    @Schema(description = "删除标识 0:未删除 1:删除")
    private Integer delFlag;

    /**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
