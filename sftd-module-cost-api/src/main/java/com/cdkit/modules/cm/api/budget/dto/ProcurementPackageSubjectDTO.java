package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采办包预算科目DTO
 * 用于返回采办包中原材料及主要原料的预算科目信息
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Schema(description = "采办包预算科目DTO")
@Data
public class ProcurementPackageSubjectDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**预算科目编码（关联预算科目表）*/
    @Schema(description = "预算科目编码（关联预算科目表）")
    private String budgetSubjectCode;

    /**预算科目名称*/
    @Schema(description = "预算科目名称")
    private String budgetSubjectName;

    /**金额（元，必填，支持小数）*/
    @Schema(description = "金额（元，必填，支持小数）")
    private BigDecimal amount;
}
