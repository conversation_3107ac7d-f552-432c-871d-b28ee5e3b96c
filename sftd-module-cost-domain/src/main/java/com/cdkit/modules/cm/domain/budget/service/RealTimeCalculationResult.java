package com.cdkit.modules.cm.domain.budget.service;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 季度预算实时计算结果（领域内部数据结构）
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
public class RealTimeCalculationResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**年度收入剩余预算金额（不含税，元）*/
    private BigDecimal annualRevenueRemainingBudget;

    /**年度支出剩余预算金额（不含税，元）*/
    private BigDecimal annualExpenditureRemainingBudget;

    /**项目支出预算总额（不含税，元）*/
    private BigDecimal projectExpenditureBudgetTotal;

    /**间接费预算总额（不含税，元）*/
    private BigDecimal indirectCostBudgetTotal;

    /**项目边际利润（元）*/
    private BigDecimal projectMarginalProfit;

    /**项目边际利润率*/
    private BigDecimal projectMarginalProfitRate;

    /**项目净利润（元）*/
    private BigDecimal projectNetProfit;

    /**项目净利润率*/
    private BigDecimal projectNetProfitRate;

    /**预算科目明细直接成本列表*/
    private List<SubjectDirectCostInfo> subjectDirectCostList;

    /**本中心间接成本列表*/
    private List<CenterIndirectCostInfo> centerIndirectCostList;

    /**非经营中心间接成本列表*/
    private List<NonOperatingIndirectCostInfo> nonOperatingIndirectCostList;

    /**综合管理间接成本列表*/
    private List<ComprehensiveIndirectCostInfo> comprehensiveIndirectCostList;
}
