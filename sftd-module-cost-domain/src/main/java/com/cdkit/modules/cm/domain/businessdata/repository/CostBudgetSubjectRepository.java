package com.cdkit.modules.cm.domain.businessdata.repository;

import com.cdkit.modules.cm.domain.businessdata.model.entity.CostBudgetSubjectEntity;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;

import java.util.List;

/**
 * 预算科目管理仓储接口
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface CostBudgetSubjectRepository {

    /**
     * 保存预算科目
     *
     * @param entity 预算科目实体
     * @return 保存后的实体
     */
    CostBudgetSubjectEntity save(CostBudgetSubjectEntity entity);

    /**
     * 根据ID更新预算科目
     *
     * @param entity 预算科目实体
     * @return 更新后的实体
     */
    CostBudgetSubjectEntity updateById(CostBudgetSubjectEntity entity);

    /**
     * 根据ID删除预算科目
     *
     * @param id 预算科目ID
     */
    void deleteById(String id);

    /**
     * 批量删除预算科目
     *
     * @param ids 预算科目ID列表
     */
    void deleteBatchByIds(List<String> ids);

    /**
     * 根据ID查询预算科目
     *
     * @param id 预算科目ID
     * @return 预算科目实体
     */
    CostBudgetSubjectEntity findById(String id);

    /**
     * 根据科目编码查询预算科目
     *
     * @param subjectCode 科目编码
     * @return 预算科目实体
     */
    CostBudgetSubjectEntity findBySubjectCode(String subjectCode);

    /**
     * 查询所有启用状态的预算科目
     *
     * @return 预算科目列表
     */
    List<CostBudgetSubjectEntity> findAllEnabled();

    /**
     * 根据状态查询预算科目列表
     *
     * @param subjectStatus 科目状态
     * @return 预算科目列表
     */
    List<CostBudgetSubjectEntity> findByStatus(String subjectStatus);

    /**
     * 分页查询预算科目列表
     *
     * @param queryEntity 查询条件实体
     * @param pageReq 分页请求
     * @return 分页结果
     */
    PageRes<CostBudgetSubjectEntity> queryPageList(CostBudgetSubjectEntity queryEntity, PageReq pageReq);

    /**
     * 检查科目编码是否存在（排除指定ID）
     *
     * @param subjectCode 科目编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsBySubjectCodeAndIdNot(String subjectCode, String excludeId);

    /**
     * 根据科目名称模糊查询
     *
     * @param subjectName 科目名称
     * @return 预算科目列表
     */
    List<CostBudgetSubjectEntity> findBySubjectNameLike(String subjectName);

    /**
     * 查询原材料和主要原料相关的预算科目
     * 通过科目名称关键字匹配原材料和主要原料相关的科目
     *
     * @return 原材料和主要原料预算科目列表
     */
    List<CostBudgetSubjectEntity> findRawMaterialAndMainMaterialSubjects();
}
