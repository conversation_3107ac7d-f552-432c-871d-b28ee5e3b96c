package com.cdkit.modules.cm.domain.budget.service.impl;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetRepository;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetSubmitService;
import com.cdkit.modules.cm.domain.budget.valobj.BudgetStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 季度预算提交领域服务实现
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetSubmitServiceImpl implements QuarterlyBudgetSubmitService {

    private final CostQuarterlyBudgetRepository costQuarterlyBudgetRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SubmitResult submitQuarterlyBudgets(SubmitRequest submitRequest) {
        log.info("开始提交季度预算，预算数量: {}, 是否强制提交: {}", 
                submitRequest.getBudgetCount(), submitRequest.getForceSubmit());

        // 1. 参数校验
        validateSubmitRequest(submitRequest);

        // 2. 查询所有待提交的预算
        List<CostQuarterlyBudgetEntity> budgetEntities = costQuarterlyBudgetRepository.findByIds(submitRequest.getBudgetIds());
        
        // 3. 验证预算是否存在
        validateBudgetExistence(submitRequest.getBudgetIds(), budgetEntities);

        // 4. 验证提交权限和状态
        List<String> successBudgetIds = new ArrayList<>();
        List<SubmitFailureDetail> failureDetails = new ArrayList<>();
        
        for (CostQuarterlyBudgetEntity entity : budgetEntities) {
            try {
                // 验证状态是否可以提交
                if (!BudgetStatusEnum.canSubmit(entity.getBudgetStatus())) {
                    String statusName = BudgetStatusEnum.getNameByCode(entity.getBudgetStatus());
                    failureDetails.add(new SubmitFailureDetail(
                            entity.getId(),
                            entity.getQuarterlyBudgetNo(),
                            entity.getQuarterlyBudgetName(),
                            statusName,
                            String.format("当前状态为[%s]，不允许提交。只有[待锁定]和[已驳回]状态的预算可以提交", statusName)
                    ));
                    continue;
                }

                // 验证必要数据完整性（可根据业务需求扩展）
                String validationError = validateBudgetData(entity);
                if (StringUtils.hasText(validationError)) {
                    failureDetails.add(new SubmitFailureDetail(
                            entity.getId(),
                            entity.getQuarterlyBudgetNo(),
                            entity.getQuarterlyBudgetName(),
                            BudgetStatusEnum.getNameByCode(entity.getBudgetStatus()),
                            validationError
                    ));
                    continue;
                }

                successBudgetIds.add(entity.getId());
                
            } catch (Exception e) {
                log.error("验证季度预算提交权限失败，预算ID: {}, 预算单号: {}", 
                        entity.getId(), entity.getQuarterlyBudgetNo(), e);
                failureDetails.add(new SubmitFailureDetail(
                        entity.getId(),
                        entity.getQuarterlyBudgetNo(),
                        entity.getQuarterlyBudgetName(),
                        BudgetStatusEnum.getNameByCode(entity.getBudgetStatus()),
                        "系统异常：" + e.getMessage()
                ));
            }
        }

        // 5. 批量更新状态为审批中
        if (!successBudgetIds.isEmpty()) {
            try {
                costQuarterlyBudgetRepository.updateBudgetStatusBatch(successBudgetIds, BudgetStatusEnum.getSubmittedStatus());
                log.info("批量更新季度预算状态为审批中成功，更新数量: {}", successBudgetIds.size());
            } catch (Exception e) {
                log.error("批量更新季度预算状态失败", e);
                throw new RuntimeException("提交失败：更新预算状态时发生异常 - " + e.getMessage());
            }
        }

        // 6. 构建返回结果
        SubmitResult result = buildSubmitResult(successBudgetIds, failureDetails, submitRequest.getBudgetCount());
        
        log.info("季度预算提交完成，总数量: {}, 成功: {}, 失败: {}", 
                result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());

        return result;
    }

    /**
     * 验证提交请求参数
     */
    private void validateSubmitRequest(SubmitRequest submitRequest) {
        if (submitRequest == null) {
            throw new IllegalArgumentException("提交请求不能为空");
        }
        
        if (submitRequest.getBudgetIds() == null || submitRequest.getBudgetIds().isEmpty()) {
            throw new IllegalArgumentException("季度预算ID列表不能为空");
        }

        // 验证ID格式
        for (String budgetId : submitRequest.getBudgetIds()) {
            if (!StringUtils.hasText(budgetId)) {
                throw new IllegalArgumentException("季度预算ID不能为空");
            }
        }
    }

    /**
     * 验证预算是否存在
     */
    private void validateBudgetExistence(List<String> requestIds, List<CostQuarterlyBudgetEntity> foundEntities) {
        if (foundEntities.size() != requestIds.size()) {
            List<String> foundIds = foundEntities.stream()
                    .map(CostQuarterlyBudgetEntity::getId)
                    .collect(Collectors.toList());
            
            List<String> notFoundIds = requestIds.stream()
                    .filter(id -> !foundIds.contains(id))
                    .collect(Collectors.toList());
            
            throw new IllegalArgumentException("部分季度预算不存在，ID: " + String.join(", ", notFoundIds));
        }
    }

    /**
     * 验证预算数据完整性
     * 可根据业务需求扩展验证规则
     */
    private String validateBudgetData(CostQuarterlyBudgetEntity entity) {
        // 验证基本信息
        if (!StringUtils.hasText(entity.getQuarterlyBudgetName())) {
            return "季度预算名称不能为空";
        }
        
        if (!StringUtils.hasText(entity.getQuarterlyPlanNo())) {
            return "关联季度计划不能为空";
        }

        if (!StringUtils.hasText(entity.getAnnualBudgetCode())) {
            return "关联年度预算不能为空";
        }

        // 验证金额数据
        if (entity.getRevenueBudgetAmount() == null || entity.getRevenueBudgetAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return "收入预算金额必须大于0";
        }
        
        if (entity.getExpenditureBudgetAmount() == null || entity.getExpenditureBudgetAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return "支出预算金额必须大于0";
        }

        return null; // 验证通过
    }

    /**
     * 构建提交结果
     */
    private SubmitResult buildSubmitResult(List<String> successBudgetIds, List<SubmitFailureDetail> failureDetails, int totalCount) {
        int successCount = successBudgetIds.size();
        int failureCount = failureDetails.size();
        
        String message;
        if (failureCount == 0) {
            message = String.format("提交成功，共%d条季度预算已进入审批流程", successCount);
        } else if (successCount == 0) {
            message = String.format("提交失败，共%d条季度预算提交失败", failureCount);
        } else {
            message = String.format("部分提交成功，成功%d条，失败%d条", successCount, failureCount);
        }

        return new SubmitResult(successCount, failureCount, totalCount, successBudgetIds, failureDetails, message);
    }
}
