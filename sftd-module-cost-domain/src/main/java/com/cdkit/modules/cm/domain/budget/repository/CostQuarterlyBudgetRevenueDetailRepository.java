package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetRevenueDetailEntity;

import java.util.List;

/**
 * 季度预算收入明细仓储接口
 * 定义收入明细的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public interface CostQuarterlyBudgetRevenueDetailRepository {

    /**
     * 保存收入明细
     *
     * @param entity 收入明细实体
     * @return 保存结果
     */
    CostQuarterlyBudgetRevenueDetailEntity save(CostQuarterlyBudgetRevenueDetailEntity entity);

    /**
     * 批量保存收入明细
     *
     * @param entities 收入明细实体列表
     * @return 保存成功的数量
     */
    int saveBatch(List<CostQuarterlyBudgetRevenueDetailEntity> entities);

    /**
     * 根据ID更新收入明细
     *
     * @param entity 收入明细实体
     * @return 更新结果
     */
    CostQuarterlyBudgetRevenueDetailEntity updateById(CostQuarterlyBudgetRevenueDetailEntity entity);

    /**
     * 根据ID删除收入明细
     *
     * @param id 收入明细ID
     */
    void deleteById(String id);

    /**
     * 批量删除收入明细
     *
     * @param ids 收入明细ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 根据季度预算ID删除收入明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 删除的数量
     */
    int deleteByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据ID查询收入明细
     *
     * @param id 收入明细ID
     * @return 收入明细实体
     */
    CostQuarterlyBudgetRevenueDetailEntity findById(String id);

    /**
     * 根据ID列表查询收入明细
     *
     * @param ids 收入明细ID列表
     * @return 收入明细实体列表
     */
    List<CostQuarterlyBudgetRevenueDetailEntity> findByIds(List<String> ids);

    /**
     * 根据季度预算ID查询收入明细列表
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 收入明细实体列表
     */
    List<CostQuarterlyBudgetRevenueDetailEntity> findByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID和物料编码查询收入明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @param materialCode 物料编码
     * @return 收入明细实体
     */
    CostQuarterlyBudgetRevenueDetailEntity findByQuarterlyBudgetIdAndMaterialCode(String quarterlyBudgetId, String materialCode);

    /**
     * 根据季度预算ID汇总年度应收预算
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 年度应收预算总额
     */
    java.math.BigDecimal sumAnnualReceivableBudgetByQuarterlyBudgetId(String quarterlyBudgetId);
}
