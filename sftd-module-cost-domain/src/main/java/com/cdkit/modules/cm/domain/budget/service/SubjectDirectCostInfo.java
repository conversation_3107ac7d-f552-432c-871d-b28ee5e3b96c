package com.cdkit.modules.cm.domain.budget.service;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预算科目明细直接成本信息（领域内部数据结构）
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
public class SubjectDirectCostInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**直接成本-预算科目编码（关联预算科目表）*/
    private String budgetSubjectCode;

    /**直接成本-预算科目名称*/
    private String budgetSubjectName;

    /**直接成本-科目释义*/
    private String subjectDescription;

    /**年度支出预算金额（不含税，元）*/
    private BigDecimal annualExpenditureBudgetAmount;

    /**年度剩余支出预算金额（不含税，元）*/
    private BigDecimal annualRemainExpendBudget;

    /**间接费预算参考金额（不含税，元）*/
    private BigDecimal indirectCostReferenceAmount;

    /**间接费预算金额（不含税，元）*/
    private BigDecimal indirectCostBudgetAmount;

    /**支出预算金额（不含税，元）*/
    private BigDecimal expenditureBudgetAmount;
}
