package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetProcPkgDetailEntity;

import java.util.List;

/**
 * 季度预算采办包明细仓储接口
 * 定义采办包明细的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public interface CostQuarterlyBudgetProcPkgDetailRepository {

    /**
     * 保存采办包明细
     *
     * @param entity 采办包明细实体
     * @return 保存结果
     */
    CostQuarterlyBudgetProcPkgDetailEntity save(CostQuarterlyBudgetProcPkgDetailEntity entity);

    /**
     * 批量保存采办包明细
     *
     * @param entities 采办包明细实体列表
     * @return 保存成功的数量
     */
    int saveBatch(List<CostQuarterlyBudgetProcPkgDetailEntity> entities);

    /**
     * 根据ID更新采办包明细
     *
     * @param entity 采办包明细实体
     * @return 更新结果
     */
    CostQuarterlyBudgetProcPkgDetailEntity updateById(CostQuarterlyBudgetProcPkgDetailEntity entity);

    /**
     * 根据ID删除采办包明细
     *
     * @param id 采办包明细ID
     */
    void deleteById(String id);

    /**
     * 批量删除采办包明细
     *
     * @param ids 采办包明细ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 根据季度预算ID删除采办包明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 删除的数量
     */
    int deleteByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据ID查询采办包明细
     *
     * @param id 采办包明细ID
     * @return 采办包明细实体
     */
    CostQuarterlyBudgetProcPkgDetailEntity findById(String id);

    /**
     * 根据ID列表查询采办包明细
     *
     * @param ids 采办包明细ID列表
     * @return 采办包明细实体列表
     */
    List<CostQuarterlyBudgetProcPkgDetailEntity> findByIds(List<String> ids);

    /**
     * 根据季度预算ID查询采办包明细列表
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 采办包明细实体列表
     */
    List<CostQuarterlyBudgetProcPkgDetailEntity> findByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID和预算科目编码查询采办包明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @param budgetSubjectCode 预算科目编码
     * @return 采办包明细实体列表
     */
    List<CostQuarterlyBudgetProcPkgDetailEntity> findByQuarterlyBudgetIdAndSubjectCode(String quarterlyBudgetId, String budgetSubjectCode);

    /**
     * 根据季度预算ID汇总采办包金额
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 采办包总金额
     */
    java.math.BigDecimal sumAmountByQuarterlyBudgetId(String quarterlyBudgetId);
}
