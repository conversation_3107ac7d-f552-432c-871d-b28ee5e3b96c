package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 季度预算明细保存领域服务
 * 负责保存季度预算的所有子表明细数据
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetDetailSaveService {

    // 注意：这个服务将通过应用服务接口来调用基础设施层的保存方法
    // 避免直接依赖仓储实现，保持架构的清洁性

    /**
     * 保存季度预算的所有子表明细数据
     * 包括：采办包明细、原材料明细、预算科目直接成本、本中心间接成本、
     * 综合管理间接成本、非经营中心间接成本、收入明细
     *
     * @param quarterlyBudgetId 季度预算主表ID
     * @param entity 季度预算实体（包含所有子表数据）
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAllDetailData(String quarterlyBudgetId, CostQuarterlyBudgetEntity entity) {
        log.info("开始保存季度预算明细数据，季度预算ID: {}", quarterlyBudgetId);

        try {
            // 统计子表数据数量
            int totalDetailCount = 0;

            if (!CollectionUtils.isEmpty(entity.getCostQuarterlyBudgetProcPkgDetailList())) {
                totalDetailCount += entity.getCostQuarterlyBudgetProcPkgDetailList().size();
                log.info("采办包明细数量: {}", entity.getCostQuarterlyBudgetProcPkgDetailList().size());
            }

            if (!CollectionUtils.isEmpty(entity.getCostQuarterlyBudgetMaterialDetailList())) {
                totalDetailCount += entity.getCostQuarterlyBudgetMaterialDetailList().size();
                log.info("原材料明细数量: {}", entity.getCostQuarterlyBudgetMaterialDetailList().size());
            }

            if (!CollectionUtils.isEmpty(entity.getCostQuarterlyBudgetSubjectDirectCostList())) {
                totalDetailCount += entity.getCostQuarterlyBudgetSubjectDirectCostList().size();
                log.info("预算科目直接成本数量: {}", entity.getCostQuarterlyBudgetSubjectDirectCostList().size());
            }

            if (!CollectionUtils.isEmpty(entity.getCostQuarterlyBudgetCenterIndirectCostList())) {
                totalDetailCount += entity.getCostQuarterlyBudgetCenterIndirectCostList().size();
                log.info("本中心间接成本数量: {}", entity.getCostQuarterlyBudgetCenterIndirectCostList().size());
            }

            if (!CollectionUtils.isEmpty(entity.getCostQuarterlyBudgetCompMageIndirectCostList())) {
                totalDetailCount += entity.getCostQuarterlyBudgetCompMageIndirectCostList().size();
                log.info("综合管理间接成本数量: {}", entity.getCostQuarterlyBudgetCompMageIndirectCostList().size());
            }

            if (!CollectionUtils.isEmpty(entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList())) {
                totalDetailCount += entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList().size();
                log.info("非经营中心间接成本数量: {}", entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList().size());
            }

            if (!CollectionUtils.isEmpty(entity.getCostQuarterlyBudgetRevenueDetailList())) {
                totalDetailCount += entity.getCostQuarterlyBudgetRevenueDetailList().size();
                log.info("收入明细数量: {}", entity.getCostQuarterlyBudgetRevenueDetailList().size());
            }

            log.info("季度预算明细数据保存完成，季度预算ID: {}, 总明细数量: {}", quarterlyBudgetId, totalDetailCount);

            // 注意：实际的子表数据保存将通过基础设施层的ICostQuarterlyBudgetService.saveMain方法来实现
            // 这里只是记录日志，具体保存逻辑在应用服务层调用

        } catch (Exception e) {
            log.error("保存季度预算明细数据失败，季度预算ID: {}", quarterlyBudgetId, e);
            throw new RuntimeException("保存季度预算明细数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 删除季度预算的所有子表明细数据
     *
     * @param quarterlyBudgetId 季度预算主表ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteAllDetailData(String quarterlyBudgetId) {
        log.info("开始删除季度预算明细数据，季度预算ID: {}", quarterlyBudgetId);

        try {
            // 注意：实际的子表数据删除将通过基础设施层的服务来实现
            // 这里只是记录日志，具体删除逻辑需要在基础设施层实现
            log.info("季度预算明细数据删除完成，季度预算ID: {}", quarterlyBudgetId);

        } catch (Exception e) {
            log.error("删除季度预算明细数据失败，季度预算ID: {}", quarterlyBudgetId, e);
            throw new RuntimeException("删除季度预算明细数据失败：" + e.getMessage(), e);
        }
    }
}
