package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetMaterialDetailEntity;

import java.util.List;

/**
 * 季度预算原材料明细仓储接口
 * 定义原材料明细的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public interface CostQuarterlyBudgetMaterialDetailRepository {

    /**
     * 保存原材料明细
     *
     * @param entity 原材料明细实体
     * @return 保存结果
     */
    CostQuarterlyBudgetMaterialDetailEntity save(CostQuarterlyBudgetMaterialDetailEntity entity);

    /**
     * 批量保存原材料明细
     *
     * @param entities 原材料明细实体列表
     * @return 保存成功的数量
     */
    int saveBatch(List<CostQuarterlyBudgetMaterialDetailEntity> entities);

    /**
     * 根据ID更新原材料明细
     *
     * @param entity 原材料明细实体
     * @return 更新结果
     */
    CostQuarterlyBudgetMaterialDetailEntity updateById(CostQuarterlyBudgetMaterialDetailEntity entity);

    /**
     * 根据ID删除原材料明细
     *
     * @param id 原材料明细ID
     */
    void deleteById(String id);

    /**
     * 批量删除原材料明细
     *
     * @param ids 原材料明细ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 根据季度预算ID删除原材料明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 删除的数量
     */
    int deleteByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据ID查询原材料明细
     *
     * @param id 原材料明细ID
     * @return 原材料明细实体
     */
    CostQuarterlyBudgetMaterialDetailEntity findById(String id);

    /**
     * 根据ID列表查询原材料明细
     *
     * @param ids 原材料明细ID列表
     * @return 原材料明细实体列表
     */
    List<CostQuarterlyBudgetMaterialDetailEntity> findByIds(List<String> ids);

    /**
     * 根据季度预算ID查询原材料明细列表
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 原材料明细实体列表
     */
    List<CostQuarterlyBudgetMaterialDetailEntity> findByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID和物料编码查询原材料明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @param materialCode 物料编码
     * @return 原材料明细实体
     */
    CostQuarterlyBudgetMaterialDetailEntity findByQuarterlyBudgetIdAndMaterialCode(String quarterlyBudgetId, String materialCode);

    /**
     * 根据季度预算ID汇总原材料总价
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 原材料总价（不含税）
     */
    java.math.BigDecimal sumTotalPriceExcludingTaxByQuarterlyBudgetId(String quarterlyBudgetId);
}
