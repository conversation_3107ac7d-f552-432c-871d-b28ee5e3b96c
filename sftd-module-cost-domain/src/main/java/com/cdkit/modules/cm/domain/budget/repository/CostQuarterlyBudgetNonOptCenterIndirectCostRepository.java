package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetNonOptCenterIndirectCostEntity;

import java.util.List;

/**
 * 季度预算-预算科目明细非经营中心间接成本仓储接口
 * 定义非经营中心间接成本的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public interface CostQuarterlyBudgetNonOptCenterIndirectCostRepository {

    /**
     * 保存非经营中心间接成本
     *
     * @param entity 非经营中心间接成本实体
     * @return 保存结果
     */
    CostQuarterlyBudgetNonOptCenterIndirectCostEntity save(CostQuarterlyBudgetNonOptCenterIndirectCostEntity entity);

    /**
     * 批量保存非经营中心间接成本
     *
     * @param entities 非经营中心间接成本实体列表
     * @return 保存成功的数量
     */
    int saveBatch(List<CostQuarterlyBudgetNonOptCenterIndirectCostEntity> entities);

    /**
     * 根据ID更新非经营中心间接成本
     *
     * @param entity 非经营中心间接成本实体
     * @return 更新结果
     */
    CostQuarterlyBudgetNonOptCenterIndirectCostEntity updateById(CostQuarterlyBudgetNonOptCenterIndirectCostEntity entity);

    /**
     * 根据ID删除非经营中心间接成本
     *
     * @param id 非经营中心间接成本ID
     */
    void deleteById(String id);

    /**
     * 批量删除非经营中心间接成本
     *
     * @param ids 非经营中心间接成本ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 根据季度预算ID删除非经营中心间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 删除的数量
     */
    int deleteByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据ID查询非经营中心间接成本
     *
     * @param id 非经营中心间接成本ID
     * @return 非经营中心间接成本实体
     */
    CostQuarterlyBudgetNonOptCenterIndirectCostEntity findById(String id);

    /**
     * 根据ID列表查询非经营中心间接成本
     *
     * @param ids 非经营中心间接成本ID列表
     * @return 非经营中心间接成本实体列表
     */
    List<CostQuarterlyBudgetNonOptCenterIndirectCostEntity> findByIds(List<String> ids);

    /**
     * 根据季度预算ID查询非经营中心间接成本列表
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 非经营中心间接成本实体列表
     */
    List<CostQuarterlyBudgetNonOptCenterIndirectCostEntity> findByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID和预算科目编码查询非经营中心间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @param budgetSubjectCode 预算科目编码
     * @return 非经营中心间接成本实体
     */
    CostQuarterlyBudgetNonOptCenterIndirectCostEntity findByQuarterlyBudgetIdAndSubjectCode(String quarterlyBudgetId, String budgetSubjectCode);

    /**
     * 根据季度预算ID汇总非经营中心间接成本总额
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 非经营中心间接成本总额
     */
    java.math.BigDecimal sumExpenditureBudgetAmountByQuarterlyBudgetId(String quarterlyBudgetId);
}
