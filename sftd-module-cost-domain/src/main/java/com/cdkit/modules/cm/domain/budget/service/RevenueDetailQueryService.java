package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.businessdata.repository.CostMaterialPriceRepository;
import com.cdkit.modules.cm.domain.gateway.material.MaterialGateway;
import com.cdkit.modules.cm.domain.gateway.material.entity.MaterialDetailDTO;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanDetailEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 收入明细查询领域服务
 * 负责将项目计划明细转换为收入明细数据
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RevenueDetailQueryService {

    private final CostProjectPlanRepository costProjectPlanRepository;
    private final MaterialGateway materialGateway;

    /**
     * 根据季度计划ID查询收入明细数据
     * 
     * @param quarterlyPlanId 季度计划ID
     * @return 收入明细数据列表
     */
    public List<RevenueDetailInfo> queryRevenueDetailByQuarterlyPlanId(String quarterlyPlanId) {
        if (!StringUtils.hasText(quarterlyPlanId)) {
            throw new IllegalArgumentException("季度计划ID不能为空");
        }

        log.info("开始查询收入明细数据，quarterlyPlanId: {}", quarterlyPlanId);

        try {
            // 1. 查询季度计划基本信息（获取合同类型）
            CostProjectPlanEntity quarterlyPlan = costProjectPlanRepository.getDomainById(quarterlyPlanId);
            if (quarterlyPlan == null) {
                log.warn("季度计划不存在，quarterlyPlanId: {}", quarterlyPlanId);
                return new ArrayList<>();
            }

            // 2. 查询项目计划明细
            List<CostProjectPlanDetailEntity> planDetailList = costProjectPlanRepository.queryDetailByPlanId(quarterlyPlanId);
            if (planDetailList == null || planDetailList.isEmpty()) {
                log.info("未找到项目计划明细数据，quarterlyPlanId: {}", quarterlyPlanId);
                return new ArrayList<>();
            }

            // 3. 转换为收入明细数据
            List<RevenueDetailInfo> revenueDetailList = new ArrayList<>();
            for (CostProjectPlanDetailEntity planDetail : planDetailList) {
                RevenueDetailInfo revenueDetail = convertToRevenueDetail(planDetail, quarterlyPlan);
                if (revenueDetail != null) {
                    revenueDetailList.add(revenueDetail);
                }
            }

            log.info("查询收入明细数据成功，quarterlyPlanId: {}, 转换后数据条数: {}", 
                    quarterlyPlanId, revenueDetailList.size());

            return revenueDetailList;

        } catch (Exception e) {
            log.error("查询收入明细数据失败，quarterlyPlanId: {}", quarterlyPlanId, e);
            throw new RuntimeException("查询收入明细数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 将项目计划明细转换为收入明细数据
     * 
     * @param planDetail 项目计划明细
     * @param quarterlyPlan 季度计划信息
     * @return 收入明细数据
     */
    private RevenueDetailInfo convertToRevenueDetail(CostProjectPlanDetailEntity planDetail, CostProjectPlanEntity quarterlyPlan) {
        try {
            RevenueDetailInfo revenueDetail = new RevenueDetailInfo();

            // 基本信息映射
            revenueDetail.setMaterialCode(planDetail.getMaterialCode());
            revenueDetail.setMaterialName(planDetail.getMaterialName());

            // 从季度计划获取合同类型
            String contractMode = quarterlyPlan.getContractMode();
            revenueDetail.setContractType(convertContractMode(contractMode));

            // 产品数量 = 用量
            revenueDetail.setProductQuantity(planDetail.getUsageAmount());

            // 预计年处理量：取有效值（油或水）
            BigDecimal estimatedAnnualProcessVolume = getEstimatedAnnualProcessVolume(planDetail);
            revenueDetail.setEstimatedAnnualProcessVolume(estimatedAnnualProcessVolume);

            // 单价 = 收费费率
            revenueDetail.setUnitPrice(planDetail.getFeeRate());

            // 年度应收预算 = 年度预算应收(油) + 年度预算应收(水)，单位转换：万元→元
            BigDecimal annualReceivableBudget = calculateAnnualReceivableBudget(planDetail);
            revenueDetail.setAnnualReceivableBudget(annualReceivableBudget);

            // 从物料主数据获取单位信息
            String unit = getMaterialUnit(planDetail.getMaterialCode());
            revenueDetail.setUnit(unit);

            // 编制依据和备注
            revenueDetail.setCompilationBasis("根据项目计划明细自动生成");
            revenueDetail.setRemark("");

            // 系统字段
            revenueDetail.setCreateTime(planDetail.getCreateTime());
            revenueDetail.setCreateBy(planDetail.getCreateBy());
            revenueDetail.setUpdateTime(planDetail.getUpdateTime());
            revenueDetail.setUpdateBy(planDetail.getUpdateBy());
            revenueDetail.setTenantId(planDetail.getTenantId());
            revenueDetail.setDelFlag(planDetail.getDelFlag());
            revenueDetail.setSysOrgCode(planDetail.getSysOrgCode());

            return revenueDetail;

        } catch (Exception e) {
            log.error("转换收入明细数据失败，planDetail: {}", planDetail.getId(), e);
            return null;
        }
    }

    /**
     * 转换合同模式为合同类型
     * 
     * @param contractMode 合同模式
     * @return 合同类型
     */
    private String convertContractMode(String contractMode) {
        if (!StringUtils.hasText(contractMode)) {
            return "rate"; // 默认费率合同
        }

        // 根据项目中的合同模式字典值进行转换
        switch (contractMode.toLowerCase()) {
            case "lump_sum":
                return "lump_sum"; // 总价合同
            case "rate":
                return "rate"; // 费率合同
            default:
                log.warn("未知的合同模式: {}, 使用默认值 rate", contractMode);
                return "rate";
        }
    }

    /**
     * 获取预计年处理量（取有效值）
     * 
     * @param planDetail 项目计划明细
     * @return 预计年处理量
     */
    private BigDecimal getEstimatedAnnualProcessVolume(CostProjectPlanDetailEntity planDetail) {
        BigDecimal oilVolume = planDetail.getEstimatedAnnualOil();
        BigDecimal waterVolume = planDetail.getEstimatedAnnualWater();

        // 优先取油的处理量，如果为空或为0则取水的处理量
        if (oilVolume != null && oilVolume.compareTo(BigDecimal.ZERO) > 0) {
            return oilVolume;
        } else if (waterVolume != null && waterVolume.compareTo(BigDecimal.ZERO) > 0) {
            return waterVolume;
        } else {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算年度应收预算（万元转元）
     * 
     * @param planDetail 项目计划明细
     * @return 年度应收预算（元）
     */
    private BigDecimal calculateAnnualReceivableBudget(CostProjectPlanDetailEntity planDetail) {
        BigDecimal revenueOil = planDetail.getRevenueOil() != null ? planDetail.getRevenueOil() : BigDecimal.ZERO;
        BigDecimal revenueWater = planDetail.getRevenueWater() != null ? planDetail.getRevenueWater() : BigDecimal.ZERO;

        // 总收入（万元）
        BigDecimal totalRevenue = revenueOil.add(revenueWater);

        // 转换为元：万元 × 10000
        return totalRevenue.multiply(new BigDecimal("10000"))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 从物料主数据获取单位信息
     * 
     * @param materialCode 物料编码
     * @return 单位
     */
    private String getMaterialUnit(String materialCode) {
        if (!StringUtils.hasText(materialCode)) {
            return "方"; // 默认单位
        }

        try {
            // 调用物料网关查询物料信息
            MaterialDetailDTO materialDetail = materialGateway.getMaterialByCode(materialCode);
            if (materialDetail != null && StringUtils.hasText(materialDetail.getUnit())) {
                return materialDetail.getUnit();
            }
        } catch (Exception e) {
            log.warn("查询物料单位信息失败，materialCode: {}, 使用默认单位", materialCode, e);
        }

        return "方"; // 默认单位
    }

    /**
     * 收入明细信息内部类
     */
    public static class RevenueDetailInfo {
        private String materialCode;
        private String materialName;
        private String unit;
        private String contractType;
        private BigDecimal productQuantity;
        private BigDecimal estimatedAnnualProcessVolume;
        private BigDecimal unitPrice;
        private BigDecimal annualReceivableBudget;
        private String compilationBasis;
        private String remark;
        private java.util.Date createTime;
        private String createBy;
        private java.util.Date updateTime;
        private String updateBy;
        private Integer tenantId;
        private Integer delFlag;
        private String sysOrgCode;

        // Getters and Setters
        public String getMaterialCode() { return materialCode; }
        public void setMaterialCode(String materialCode) { this.materialCode = materialCode; }
        public String getMaterialName() { return materialName; }
        public void setMaterialName(String materialName) { this.materialName = materialName; }
        public String getUnit() { return unit; }
        public void setUnit(String unit) { this.unit = unit; }
        public String getContractType() { return contractType; }
        public void setContractType(String contractType) { this.contractType = contractType; }
        public BigDecimal getProductQuantity() { return productQuantity; }
        public void setProductQuantity(BigDecimal productQuantity) { this.productQuantity = productQuantity; }
        public BigDecimal getEstimatedAnnualProcessVolume() { return estimatedAnnualProcessVolume; }
        public void setEstimatedAnnualProcessVolume(BigDecimal estimatedAnnualProcessVolume) { this.estimatedAnnualProcessVolume = estimatedAnnualProcessVolume; }
        public BigDecimal getUnitPrice() { return unitPrice; }
        public void setUnitPrice(BigDecimal unitPrice) { this.unitPrice = unitPrice; }
        public BigDecimal getAnnualReceivableBudget() { return annualReceivableBudget; }
        public void setAnnualReceivableBudget(BigDecimal annualReceivableBudget) { this.annualReceivableBudget = annualReceivableBudget; }
        public String getCompilationBasis() { return compilationBasis; }
        public void setCompilationBasis(String compilationBasis) { this.compilationBasis = compilationBasis; }
        public String getRemark() { return remark; }
        public void setRemark(String remark) { this.remark = remark; }
        public java.util.Date getCreateTime() { return createTime; }
        public void setCreateTime(java.util.Date createTime) { this.createTime = createTime; }
        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }
        public java.util.Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(java.util.Date updateTime) { this.updateTime = updateTime; }
        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }
        public Integer getTenantId() { return tenantId; }
        public void setTenantId(Integer tenantId) { this.tenantId = tenantId; }
        public Integer getDelFlag() { return delFlag; }
        public void setDelFlag(Integer delFlag) { this.delFlag = delFlag; }
        public String getSysOrgCode() { return sysOrgCode; }
        public void setSysOrgCode(String sysOrgCode) { this.sysOrgCode = sysOrgCode; }
    }
}
