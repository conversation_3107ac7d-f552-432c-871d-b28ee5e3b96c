package com.cdkit.modules.cm.domain.budget.service;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 综合管理间接成本信息（领域内部数据结构）
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
public class ComprehensiveIndirectCostInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**综合管理间接成本-预算科目编码（关联预算科目表）*/
    private String budgetSubjectCode;

    /**综合管理间接成本-预算科目名称*/
    private String budgetSubjectName;

    /**综合管理间接成本-预算释义*/
    private String subjectDescription;

    /**综合管理间接成本-支出预算金额（元）*/
    private BigDecimal expenditureBudgetAmount;
}
