package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetSubjectDirectCostEntity;

import java.util.List;

/**
 * 季度预算预算科目明细直接成本仓储接口
 * 定义预算科目直接成本的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public interface CostQuarterlyBudgetSubjectDirectCostRepository {

    /**
     * 保存预算科目直接成本
     *
     * @param entity 预算科目直接成本实体
     * @return 保存结果
     */
    CostQuarterlyBudgetSubjectDirectCostEntity save(CostQuarterlyBudgetSubjectDirectCostEntity entity);

    /**
     * 批量保存预算科目直接成本
     *
     * @param entities 预算科目直接成本实体列表
     * @return 保存成功的数量
     */
    int saveBatch(List<CostQuarterlyBudgetSubjectDirectCostEntity> entities);

    /**
     * 根据ID更新预算科目直接成本
     *
     * @param entity 预算科目直接成本实体
     * @return 更新结果
     */
    CostQuarterlyBudgetSubjectDirectCostEntity updateById(CostQuarterlyBudgetSubjectDirectCostEntity entity);

    /**
     * 根据ID删除预算科目直接成本
     *
     * @param id 预算科目直接成本ID
     */
    void deleteById(String id);

    /**
     * 批量删除预算科目直接成本
     *
     * @param ids 预算科目直接成本ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 根据季度预算ID删除预算科目直接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 删除的数量
     */
    int deleteByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据ID查询预算科目直接成本
     *
     * @param id 预算科目直接成本ID
     * @return 预算科目直接成本实体
     */
    CostQuarterlyBudgetSubjectDirectCostEntity findById(String id);

    /**
     * 根据ID列表查询预算科目直接成本
     *
     * @param ids 预算科目直接成本ID列表
     * @return 预算科目直接成本实体列表
     */
    List<CostQuarterlyBudgetSubjectDirectCostEntity> findByIds(List<String> ids);

    /**
     * 根据季度预算ID查询预算科目直接成本列表
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 预算科目直接成本实体列表
     */
    List<CostQuarterlyBudgetSubjectDirectCostEntity> findByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID和预算科目编码查询预算科目直接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @param budgetSubjectCode 预算科目编码
     * @return 预算科目直接成本实体
     */
    CostQuarterlyBudgetSubjectDirectCostEntity findByQuarterlyBudgetIdAndSubjectCode(String quarterlyBudgetId, String budgetSubjectCode);

    /**
     * 根据季度预算ID汇总支出预算金额
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 支出预算金额总额
     */
    java.math.BigDecimal sumExpenditureBudgetAmountByQuarterlyBudgetId(String quarterlyBudgetId);
}
