package com.cdkit.modules.cm.domain.budget.service;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 非经营中心间接成本信息（领域内部数据结构）
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
public class NonOperatingIndirectCostInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**非经营中心间接成本-预算科目编码（关联预算科目表）*/
    private String budgetSubjectCode;

    /**非经营中心间接成本-预算科目名称*/
    private String budgetSubjectName;

    /**非经营中心间接成本-预算释义*/
    private String subjectDescription;

    /**非经营中心间接成本-支出预算金额（元）*/
    private BigDecimal expenditureBudgetAmount;
}
