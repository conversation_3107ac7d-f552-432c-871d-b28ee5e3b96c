package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCenterIndirectCostEntity;

import java.util.List;

/**
 * 季度预算-预算科目明细本中心间接成本仓储接口
 * 定义本中心间接成本的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public interface CostQuarterlyBudgetCenterIndirectCostRepository {

    /**
     * 保存本中心间接成本
     *
     * @param entity 本中心间接成本实体
     * @return 保存结果
     */
    CostQuarterlyBudgetCenterIndirectCostEntity save(CostQuarterlyBudgetCenterIndirectCostEntity entity);

    /**
     * 批量保存本中心间接成本
     *
     * @param entities 本中心间接成本实体列表
     * @return 保存成功的数量
     */
    int saveBatch(List<CostQuarterlyBudgetCenterIndirectCostEntity> entities);

    /**
     * 根据ID更新本中心间接成本
     *
     * @param entity 本中心间接成本实体
     * @return 更新结果
     */
    CostQuarterlyBudgetCenterIndirectCostEntity updateById(CostQuarterlyBudgetCenterIndirectCostEntity entity);

    /**
     * 根据ID删除本中心间接成本
     *
     * @param id 本中心间接成本ID
     */
    void deleteById(String id);

    /**
     * 批量删除本中心间接成本
     *
     * @param ids 本中心间接成本ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 根据季度预算ID删除本中心间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 删除的数量
     */
    int deleteByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据ID查询本中心间接成本
     *
     * @param id 本中心间接成本ID
     * @return 本中心间接成本实体
     */
    CostQuarterlyBudgetCenterIndirectCostEntity findById(String id);

    /**
     * 根据ID列表查询本中心间接成本
     *
     * @param ids 本中心间接成本ID列表
     * @return 本中心间接成本实体列表
     */
    List<CostQuarterlyBudgetCenterIndirectCostEntity> findByIds(List<String> ids);

    /**
     * 根据季度预算ID查询本中心间接成本列表
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 本中心间接成本实体列表
     */
    List<CostQuarterlyBudgetCenterIndirectCostEntity> findByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID和预算科目编码查询本中心间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @param budgetSubjectCode 预算科目编码
     * @return 本中心间接成本实体
     */
    CostQuarterlyBudgetCenterIndirectCostEntity findByQuarterlyBudgetIdAndSubjectCode(String quarterlyBudgetId, String budgetSubjectCode);

    /**
     * 根据季度预算ID汇总本中心间接成本总额
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 本中心间接成本总额
     */
    java.math.BigDecimal sumExpenditureBudgetAmountByQuarterlyBudgetId(String quarterlyBudgetId);
}
