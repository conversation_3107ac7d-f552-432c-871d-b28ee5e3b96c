package com.cdkit.modules.cm.domain.budget.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预算状态枚举
 * 用于季度预算和年度预算的状态管理
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Getter
@AllArgsConstructor
public enum BudgetStatusEnum {

    /**
     * 待锁定
     */
    PENDING_LOCK("PENDING_LOCK", "待锁定"),

    /**
     * 审批中
     */
    APPROVING("APPROVING", "审批中"),

    /**
     * 已锁定
     */
    LOCKED("LOCKED", "已锁定"),

    /**
     * 已驳回
     */
    REJECTED("REJECTED", "已驳回"),

    /**
     * 已变更
     */
    CHANGED("CHANGED", "已变更");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态码获取状态名称
     *
     * @param code 状态码
     * @return 状态名称
     */
    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BudgetStatusEnum status : BudgetStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return code;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 状态枚举
     */
    public static BudgetStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BudgetStatusEnum status : BudgetStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 验证状态是否可以提交
     * 只有待锁定和已驳回状态可以提交
     *
     * @param status 当前状态
     * @return true-可以提交，false-不可提交
     */
    public static boolean canSubmit(String status) {
        return PENDING_LOCK.getCode().equals(status) || REJECTED.getCode().equals(status);
    }

    /**
     * 验证状态是否可以编辑
     * 只有待锁定和已驳回状态可以编辑
     *
     * @param status 当前状态
     * @return true-可以编辑，false-不可编辑
     */
    public static boolean canEdit(String status) {
        return PENDING_LOCK.getCode().equals(status) || REJECTED.getCode().equals(status);
    }

    /**
     * 验证状态是否可以删除
     * 只有待锁定和已驳回状态可以删除
     *
     * @param status 当前状态
     * @return true-可以删除，false-不可删除
     */
    public static boolean canDelete(String status) {
        return PENDING_LOCK.getCode().equals(status) || REJECTED.getCode().equals(status);
    }

    /**
     * 验证状态是否可以变更
     * 只有已锁定状态可以变更
     *
     * @param status 当前状态
     * @return true-可以变更，false-不可变更
     */
    public static boolean canChange(String status) {
        return LOCKED.getCode().equals(status);
    }

    /**
     * 获取提交后的状态
     * 提交后状态变为审批中
     *
     * @return 审批中状态码
     */
    public static String getSubmittedStatus() {
        return APPROVING.getCode();
    }

    /**
     * 获取审批通过后的状态
     * 审批通过后状态变为已锁定
     *
     * @return 已锁定状态码
     */
    public static String getApprovedStatus() {
        return LOCKED.getCode();
    }

    /**
     * 获取审批驳回后的状态
     * 审批驳回后状态变为已驳回
     *
     * @return 已驳回状态码
     */
    public static String getRejectedStatus() {
        return REJECTED.getCode();
    }

    /**
     * 获取变更后的状态
     * 变更后原数据状态变为已变更
     *
     * @return 已变更状态码
     */
    public static String getChangedStatus() {
        return CHANGED.getCode();
    }
}
