package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import java.util.List;

/**
 * 季度预算保存领域服务接口
 * 用于封装基础设施层的保存操作，避免应用层直接依赖基础设施层
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public interface QuarterlyBudgetSaveService {

    /**
     * 保存季度预算主表和所有子表数据
     * 注意：这个方法将通过基础设施层的ICostQuarterlyBudgetService.saveMain来实现
     * 
     * @param entity 季度预算实体（包含所有子表数据）
     * @return 保存后的主表ID
     */
    String saveMainWithDetails(CostQuarterlyBudgetEntity entity);

    /**
     * 更新季度预算主表和所有子表数据
     * 
     * @param entity 季度预算实体（包含所有子表数据）
     * @return 更新后的主表ID
     */
    String updateMainWithDetails(CostQuarterlyBudgetEntity entity);

    /**
     * 删除季度预算主表和所有子表数据
     *
     * @param id 主表ID
     */
    void deleteMainWithDetails(String id);

    /**
     * 批量删除季度预算主表和所有子表数据
     *
     * @param idList 主表ID列表
     */
    void deleteBatchMainWithDetails(List<String> idList);
}
