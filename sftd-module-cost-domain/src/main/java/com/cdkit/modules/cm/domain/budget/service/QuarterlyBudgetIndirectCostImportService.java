package com.cdkit.modules.cm.domain.budget.service;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 季度预算间接成本导入领域服务
 * 负责处理间接成本数据的导入、校验和保存逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetIndirectCostImportService {

    // 移除实时计算服务依赖，简化业务流程

    /**
     * 执行间接成本导入
     * 注意：只进行Excel数据解析和校验，不触发实时计算，不保存到数据库
     *
     * @param request 导入请求参数
     * @return 导入结果
     */
    public IndirectCostImportResult importIndirectCost(IndirectCostImportRequest request) {
        log.info("开始执行季度预算间接成本导入，季度计划ID: {}, 间接成本类型: {}, 数据条数: {}",
                request.getQuarterlyPlanId(), request.getIndirectCostType(),
                request.getImportDataList() != null ? request.getImportDataList().size() : 0);

        try {
            // 1. 数据校验
            ValidationResult validationResult = validateImportData(request.getImportDataList());

            // 2. 构建导入结果（只包含解析和校验结果，不进行实时计算）
            IndirectCostImportResult importResult = new IndirectCostImportResult();
            importResult.setSuccessCount(validationResult.getValidDataList().size());
            importResult.setFailureCount(validationResult.getInvalidDataList().size());
            importResult.setSuccessList(validationResult.getValidDataList());
            importResult.setFailureList(validationResult.getInvalidDataList());

            // 不再触发实时计算，提高响应速度
            importResult.setCalculationResult(null);

            log.info("季度预算间接成本导入完成，成功: {}条, 失败: {}条",
                    importResult.getSuccessCount(), importResult.getFailureCount());

            return importResult;

        } catch (Exception e) {
            log.error("季度预算间接成本导入失败，季度计划ID: {}", request.getQuarterlyPlanId(), e);
            throw e;
        }
    }

    /**
     * 校验导入数据
     */
    private ValidationResult validateImportData(List<IndirectCostImportItem> importDataList) {
        ValidationResult result = new ValidationResult();
        List<IndirectCostImportItem> validDataList = new ArrayList<>();
        List<ImportFailureItem> invalidDataList = new ArrayList<>();

        if (importDataList == null || importDataList.isEmpty()) {
            log.warn("导入数据为空");
            result.setValidDataList(validDataList);
            result.setInvalidDataList(invalidDataList);
            return result;
        }

        for (int i = 0; i < importDataList.size(); i++) {
            IndirectCostImportItem item = importDataList.get(i);
            item.setRowNumber(i + 2); // Excel行号从第2行开始（第1行是标题）

            String errorMessage = validateSingleItem(item);
            if (errorMessage == null) {
                validDataList.add(item);
            } else {
                ImportFailureItem failureItem = new ImportFailureItem();
                failureItem.setRowNumber(item.getRowNumber());
                failureItem.setOriginalData(item);
                failureItem.setErrorMessage(errorMessage);
                invalidDataList.add(failureItem);
            }
        }

        result.setValidDataList(validDataList);
        result.setInvalidDataList(invalidDataList);

        log.info("数据校验完成，有效数据: {}条, 无效数据: {}条", validDataList.size(), invalidDataList.size());
        return result;
    }

    /**
     * 校验单个数据项
     */
    private String validateSingleItem(IndirectCostImportItem item) {
        // 1. 必填字段校验
        if (!StringUtils.hasText(item.getBudgetSubjectCode())) {
            return "预算科目编码不能为空";
        }
        if (!StringUtils.hasText(item.getBudgetSubjectName())) {
            return "预算科目名称不能为空";
        }
        if (item.getExpenditureBudgetAmount() == null) {
            return "支出预算金额不能为空";
        }

        // 2. 金额范围校验
        if (item.getExpenditureBudgetAmount().compareTo(BigDecimal.ZERO) < 0) {
            return "支出预算金额不能为负数";
        }

        // 3. 预算科目编码格式校验（可根据实际规则调整）
        if (item.getBudgetSubjectCode().length() > 50) {
            return "预算科目编码长度不能超过50个字符";
        }

        // TODO: 4. 预算科目编码有效性校验（需要查询预算科目表）

        return null; // 校验通过
    }

    // 移除实时计算相关方法，简化业务流程，提高响应速度

    /**
     * 数据校验结果
     */
    @Data
    public static class ValidationResult implements Serializable {
        private static final long serialVersionUID = 1L;

        /**有效数据列表*/
        private List<IndirectCostImportItem> validDataList;

        /**无效数据列表*/
        private List<ImportFailureItem> invalidDataList;
    }

    /**
     * 间接成本导入请求
     */
    @Data
    public static class IndirectCostImportRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        /**季度计划ID*/
        private String quarterlyPlanId;

        /**年度预算ID*/
        private String annualBudgetId;

        /**间接成本类型*/
        private IndirectCostType indirectCostType;

        /**导入数据列表*/
        private List<IndirectCostImportItem> importDataList;
    }

    /**
     * 间接成本导入项
     */
    @Data
    public static class IndirectCostImportItem implements Serializable {
        private static final long serialVersionUID = 1L;

        /**行号*/
        private Integer rowNumber;

        /**预算科目编码*/
        private String budgetSubjectCode;

        /**预算科目名称*/
        private String budgetSubjectName;

        /**科目释义*/
        private String subjectDescription;

        /**支出预算金额（元）*/
        private BigDecimal expenditureBudgetAmount;

        /**备注*/
        private String remark;
    }

    /**
     * 间接成本导入结果
     */
    @Data
    public static class IndirectCostImportResult implements Serializable {
        private static final long serialVersionUID = 1L;

        /**成功导入数量*/
        private Integer successCount;

        /**失败导入数量*/
        private Integer failureCount;

        /**成功导入的数据列表*/
        private List<IndirectCostImportItem> successList;

        /**失败导入的数据列表*/
        private List<ImportFailureItem> failureList;

        /**实时计算结果*/
        private RealTimeCalculationResult calculationResult;
    }

    /**
     * 导入失败项
     */
    @Data
    public static class ImportFailureItem implements Serializable {
        private static final long serialVersionUID = 1L;

        /**行号*/
        private Integer rowNumber;

        /**原始数据*/
        private IndirectCostImportItem originalData;

        /**错误信息*/
        private String errorMessage;
    }

    /**
     * 间接成本类型枚举
     */
    public enum IndirectCostType {
        /**本中心间接成本*/
        CENTER_INDIRECT,
        /**非经营中心间接成本*/
        NON_OPERATING_INDIRECT,
        /**综合管理间接成本*/
        COMPREHENSIVE_INDIRECT
    }
}
