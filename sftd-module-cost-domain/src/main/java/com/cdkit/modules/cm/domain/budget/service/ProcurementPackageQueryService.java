package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.businessdata.model.entity.CostBudgetSubjectEntity;
import com.cdkit.modules.cm.domain.businessdata.repository.CostBudgetSubjectRepository;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 采办包查询领域服务
 * 负责查询采办包中原材料及主要原料的预算科目信息
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProcurementPackageQueryService {

    private final CostBudgetSubjectRepository costBudgetSubjectRepository;
    private final CostProjectPlanRepository costProjectPlanRepository;

    /**
     * 根据季度计划ID查询采办包预算科目信息
     * 
     * @param quarterlyPlanId 季度计划ID（项目计划ID）
     * @return 采办包预算科目信息列表
     */
    public List<ProcurementPackageSubjectInfo> queryProcurementPackageSubjects(String quarterlyPlanId) {
        if (!StringUtils.hasText(quarterlyPlanId)) {
            throw new IllegalArgumentException("季度计划ID不能为空");
        }

        log.info("开始查询采办包预算科目信息，季度计划ID: {}", quarterlyPlanId);

        try {
            // 1. 根据季度计划ID查询项目计划信息
            CostProjectPlanEntity projectPlan = costProjectPlanRepository.getDomainById(quarterlyPlanId);
            if (projectPlan == null) {
                log.warn("未找到对应的项目计划，季度计划ID: {}", quarterlyPlanId);
                return new ArrayList<>();
            }

            log.info("查询到项目计划信息，计划名称: {}, 直接成本小计: {}", 
                    projectPlan.getPlanName(), projectPlan.getDirectCostTotal());

            // 2. 查询原材料和主要原料相关的预算科目
            List<CostBudgetSubjectEntity> budgetSubjects = costBudgetSubjectRepository.findRawMaterialAndMainMaterialSubjects();
            if (budgetSubjects.isEmpty()) {
                log.warn("未找到原材料和主要原料相关的预算科目");
                return new ArrayList<>();
            }

            log.info("查询到{}个原材料和主要原料预算科目", budgetSubjects.size());

            // 3. 构建返回结果
            List<ProcurementPackageSubjectInfo> resultList = new ArrayList<>();
            BigDecimal directCostTotal = projectPlan.getDirectCostTotal() != null ? 
                    projectPlan.getDirectCostTotal() : BigDecimal.ZERO;

            for (CostBudgetSubjectEntity subject : budgetSubjects) {
                ProcurementPackageSubjectInfo subjectInfo = new ProcurementPackageSubjectInfo();
                subjectInfo.setBudgetSubjectCode(subject.getSubjectCode());
                subjectInfo.setBudgetSubjectName(subject.getSubjectName());
                // 使用项目计划的直接成本小计作为金额基础
                subjectInfo.setAmount(directCostTotal);
                
                resultList.add(subjectInfo);
            }

            log.info("构建采办包预算科目信息成功，共{}条记录", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("查询采办包预算科目信息失败，季度计划ID: {}", quarterlyPlanId, e);
            throw new RuntimeException("查询采办包预算科目信息失败：" + e.getMessage());
        }
    }

    /**
     * 采办包预算科目信息内部类
     */
    public static class ProcurementPackageSubjectInfo {
        /**预算科目编码*/
        private String budgetSubjectCode;
        
        /**预算科目名称*/
        private String budgetSubjectName;
        
        /**金额（元）*/
        private BigDecimal amount;

        // Getters and Setters
        public String getBudgetSubjectCode() {
            return budgetSubjectCode;
        }

        public void setBudgetSubjectCode(String budgetSubjectCode) {
            this.budgetSubjectCode = budgetSubjectCode;
        }

        public String getBudgetSubjectName() {
            return budgetSubjectName;
        }

        public void setBudgetSubjectName(String budgetSubjectName) {
            this.budgetSubjectName = budgetSubjectName;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }
    }
}
