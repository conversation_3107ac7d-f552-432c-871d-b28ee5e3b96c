package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCompMageIndirectCostEntity;

import java.util.List;

/**
 * 季度预算-预算科目明细综合管理间接成本仓储接口
 * 定义综合管理间接成本的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public interface CostQuarterlyBudgetCompMageIndirectCostRepository {

    /**
     * 保存综合管理间接成本
     *
     * @param entity 综合管理间接成本实体
     * @return 保存结果
     */
    CostQuarterlyBudgetCompMageIndirectCostEntity save(CostQuarterlyBudgetCompMageIndirectCostEntity entity);

    /**
     * 批量保存综合管理间接成本
     *
     * @param entities 综合管理间接成本实体列表
     * @return 保存成功的数量
     */
    int saveBatch(List<CostQuarterlyBudgetCompMageIndirectCostEntity> entities);

    /**
     * 根据ID更新综合管理间接成本
     *
     * @param entity 综合管理间接成本实体
     * @return 更新结果
     */
    CostQuarterlyBudgetCompMageIndirectCostEntity updateById(CostQuarterlyBudgetCompMageIndirectCostEntity entity);

    /**
     * 根据ID删除综合管理间接成本
     *
     * @param id 综合管理间接成本ID
     */
    void deleteById(String id);

    /**
     * 批量删除综合管理间接成本
     *
     * @param ids 综合管理间接成本ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 根据季度预算ID删除综合管理间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 删除的数量
     */
    int deleteByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据ID查询综合管理间接成本
     *
     * @param id 综合管理间接成本ID
     * @return 综合管理间接成本实体
     */
    CostQuarterlyBudgetCompMageIndirectCostEntity findById(String id);

    /**
     * 根据ID列表查询综合管理间接成本
     *
     * @param ids 综合管理间接成本ID列表
     * @return 综合管理间接成本实体列表
     */
    List<CostQuarterlyBudgetCompMageIndirectCostEntity> findByIds(List<String> ids);

    /**
     * 根据季度预算ID查询综合管理间接成本列表
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 综合管理间接成本实体列表
     */
    List<CostQuarterlyBudgetCompMageIndirectCostEntity> findByQuarterlyBudgetId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID和预算科目编码查询综合管理间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @param budgetSubjectCode 预算科目编码
     * @return 综合管理间接成本实体
     */
    CostQuarterlyBudgetCompMageIndirectCostEntity findByQuarterlyBudgetIdAndSubjectCode(String quarterlyBudgetId, String budgetSubjectCode);

    /**
     * 根据季度预算ID汇总综合管理间接成本总额
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 综合管理间接成本总额
     */
    java.math.BigDecimal sumExpenditureBudgetAmountByQuarterlyBudgetId(String quarterlyBudgetId);
}
