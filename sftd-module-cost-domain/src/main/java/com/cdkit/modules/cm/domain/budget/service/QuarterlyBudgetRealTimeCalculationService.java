package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetRepository;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailFullRepository;
import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetRepository;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 季度预算实时计算领域服务
 * 负责根据采办包变更数据实时计算季度预算的各项金额
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetRealTimeCalculationService {

    private final CostAnnualBudgetRepository annualBudgetRepository;
    private final CostAnnualBudgetDetailFullRepository annualBudgetDetailFullRepository;
    private final CostQuarterlyBudgetRepository quarterlyBudgetRepository;
    private final CostProjectPlanRepository projectPlanRepository;

    /**
     * 执行季度预算实时计算
     *
     * @param request 实时计算请求参数
     * @return 实时计算结果
     */
    public RealTimeCalculationResult calculate(RealTimeCalculationRequest request) {
        log.info("开始执行季度预算实时计算，季度计划ID: {}, 年度预算ID: {}",
                request.getQuarterlyPlanId(), request.getAnnualBudgetId());

        // 1. 获取季度计划信息
        CostProjectPlanEntity quarterlyPlan = projectPlanRepository.queryByIdWithDetails(request.getQuarterlyPlanId());
        if (quarterlyPlan == null) {
            throw new IllegalArgumentException("季度计划不存在，ID: " + request.getQuarterlyPlanId());
        }

        // 2. 获取项目编号
        String projectCode = quarterlyPlan.getProjectCode();
        if (!StringUtils.hasText(projectCode)) {
            throw new IllegalArgumentException("季度计划中项目编号为空，季度计划ID: " + request.getQuarterlyPlanId());
        }

        // 3. 验证年度预算ID与项目编号的匹配性
        CostAnnualBudgetDetailFullRepository.AnnualBudgetDetailFullInfo annualBudgetDetail =
            annualBudgetDetailFullRepository.findByBudgetId(request.getAnnualBudgetId());
        if (annualBudgetDetail == null) {
            throw new IllegalArgumentException("年度预算明细不存在，年度预算ID: " + request.getAnnualBudgetId());
        }

        // 4. 验证年度预算ID与项目编号的匹配性（可选验证，记录日志）
        try {
            CostAnnualBudgetDetailFullRepository.AnnualBudgetDetailFullInfo correctAnnualBudgetDetail =
                annualBudgetDetailFullRepository.findByProjectCode(projectCode);
            if (correctAnnualBudgetDetail != null && !correctAnnualBudgetDetail.getBudgetId().equals(request.getAnnualBudgetId())) {
                log.warn("年度预算ID与项目编号可能不匹配，季度计划项目编号: {}, 传入的年度预算ID: {}, 通过项目编号找到的年度预算ID: {}",
                        projectCode, request.getAnnualBudgetId(), correctAnnualBudgetDetail.getBudgetId());
            } else {
                log.info("年度预算ID与项目编号匹配验证通过");
            }
        } catch (Exception e) {
            log.warn("验证年度预算ID与项目编号匹配性时出现异常，继续使用传入的年度预算ID", e);
        }

        // 5. 获取年度预算主表信息
        CostAnnualBudgetEntity annualBudget = annualBudgetRepository.findById(request.getAnnualBudgetId());
        if (annualBudget == null) {
            throw new IllegalArgumentException("年度预算不存在，ID: " + request.getAnnualBudgetId());
        }

        log.info("验证通过，项目编号: {}, 年度预算ID: {}, 年度预算名称: {}",
                projectCode, annualBudget.getId(), annualBudget.getBudgetName());

        // 6. 计算年度收入剩余预算金额
        BigDecimal annualRevenueRemainingBudget = calculateAnnualRevenueRemainingBudget(
                request.getAnnualBudgetId(), request.getQuarterlyBudgetId());

        // 5. 计算项目收入预算总额（如果未提供则使用季度计划的合同收入）
        BigDecimal projectRevenueBudgetTotal = request.getProjectRevenueBudgetTotal();
        if (projectRevenueBudgetTotal == null || projectRevenueBudgetTotal.compareTo(BigDecimal.ZERO) <= 0) {
            projectRevenueBudgetTotal = quarterlyPlan.getContractRevenue() != null ?
                quarterlyPlan.getContractRevenue() : BigDecimal.ZERO;
        }

        // 6. 构建采办包科目金额映射
        Map<String, BigDecimal> procurementPackageAmountMap = buildProcurementPackageAmountMap(request);

        // 7. 计算间接成本（优先使用用户输入的数据，否则按比例分摊年度预算）
        List<CenterIndirectCostInfo> centerIndirectCostList =
            getOrCalculateCenterIndirectCost(request, annualBudgetDetail, projectRevenueBudgetTotal, annualBudget.getRevenueTotalAmount());

        List<NonOperatingIndirectCostInfo> nonOperatingIndirectCostList =
            getOrCalculateNonOperatingIndirectCost(request, annualBudgetDetail, projectRevenueBudgetTotal, annualBudget.getRevenueTotalAmount());

        List<ComprehensiveIndirectCostInfo> comprehensiveIndirectCostList =
            getOrCalculateComprehensiveIndirectCost(request, annualBudgetDetail, projectRevenueBudgetTotal, annualBudget.getRevenueTotalAmount());

        // 8. 计算预算科目明细直接成本（需要间接成本计算结果来计算间接费预算金额）
        List<SubjectDirectCostInfo> subjectDirectCostList =
            calculateSubjectDirectCost(annualBudgetDetail, procurementPackageAmountMap,
                    projectRevenueBudgetTotal, annualBudget.getRevenueTotalAmount(), request.getQuarterlyBudgetId(),
                    centerIndirectCostList, nonOperatingIndirectCostList, comprehensiveIndirectCostList);

        // 9. 计算汇总金额
        BigDecimal annualExpenditureRemainingBudget = calculateAnnualExpenditureRemainingBudget(subjectDirectCostList);
        BigDecimal projectExpenditureBudgetTotal = calculateProjectExpenditureBudgetTotal(subjectDirectCostList);
        BigDecimal indirectCostBudgetTotal = calculateIndirectCostBudgetTotal(subjectDirectCostList);

        // 10. 计算利润信息
        BigDecimal projectMarginalProfit = projectRevenueBudgetTotal.subtract(projectExpenditureBudgetTotal);
        BigDecimal projectMarginalProfitRate = projectRevenueBudgetTotal.compareTo(BigDecimal.ZERO) > 0 ?
            projectMarginalProfit.divide(projectRevenueBudgetTotal, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        BigDecimal projectNetProfit = projectMarginalProfit.subtract(indirectCostBudgetTotal);
        BigDecimal projectNetProfitRate = projectRevenueBudgetTotal.compareTo(BigDecimal.ZERO) > 0 ?
            projectNetProfit.divide(projectRevenueBudgetTotal, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        // 11. 构建响应结果
        RealTimeCalculationResult result = new RealTimeCalculationResult();
        result.setAnnualRevenueRemainingBudget(annualRevenueRemainingBudget);
        result.setAnnualExpenditureRemainingBudget(annualExpenditureRemainingBudget);
        result.setProjectExpenditureBudgetTotal(projectExpenditureBudgetTotal);
        result.setIndirectCostBudgetTotal(indirectCostBudgetTotal);
        result.setProjectMarginalProfit(projectMarginalProfit);
        result.setProjectMarginalProfitRate(projectMarginalProfitRate);
        result.setProjectNetProfit(projectNetProfit);
        result.setProjectNetProfitRate(projectNetProfitRate);
        result.setSubjectDirectCostList(subjectDirectCostList);
        result.setCenterIndirectCostList(centerIndirectCostList);
        result.setNonOperatingIndirectCostList(nonOperatingIndirectCostList);
        result.setComprehensiveIndirectCostList(comprehensiveIndirectCostList);

        log.info("季度预算实时计算完成，项目支出预算总额: {}元, 间接费预算总额: {}元, 项目边际利润: {}元",
                projectExpenditureBudgetTotal, indirectCostBudgetTotal, projectMarginalProfit);

        return result;
    }

    /**
     * 计算年度收入剩余预算金额
     * 年度收入剩余预算金额 = 年度预算的收入预算金额 - 与该年度预算相关的已经通过审批通过的季度预算的项目预算总额
     */
    private BigDecimal calculateAnnualRevenueRemainingBudget(String annualBudgetId, String excludeQuarterlyBudgetId) {
        // 获取年度预算信息
        CostAnnualBudgetEntity annualBudget = annualBudgetRepository.findById(annualBudgetId);
        if (annualBudget == null) {
            throw new IllegalArgumentException("年度预算不存在，ID: " + annualBudgetId);
        }

        // 获取年度预算的收入预算金额
        BigDecimal annualRevenueBudget = annualBudget.getRevenueTotalAmount() != null ?
            annualBudget.getRevenueTotalAmount() : BigDecimal.ZERO;

        // 查询与该年度预算相关的已审批通过的季度预算的项目预算总额
        BigDecimal approvedQuarterlyBudgetTotal = quarterlyBudgetRepository
            .sumApprovedQuarterlyBudgetRevenueByAnnualBudgetId(annualBudgetId, excludeQuarterlyBudgetId);

        return annualRevenueBudget.subtract(approvedQuarterlyBudgetTotal != null ? approvedQuarterlyBudgetTotal : BigDecimal.ZERO);
    }

    /**
     * 构建采办包科目金额映射
     */
    private Map<String, BigDecimal> buildProcurementPackageAmountMap(RealTimeCalculationRequest request) {
        if (request.getProcurementPackageDetails() == null || request.getProcurementPackageDetails().isEmpty()) {
            return Map.of();
        }

        return request.getProcurementPackageDetails().stream()
            .filter(detail -> StringUtils.hasText(detail.getBudgetSubjectCode()) && detail.getAmount() != null)
            .collect(Collectors.toMap(
                ProcurementPackageDetail::getBudgetSubjectCode,
                ProcurementPackageDetail::getAmount,
                (existing, replacement) -> replacement // 如果有重复的科目编码，使用后面的值
            ));
    }

    /**
     * 计算预算科目明细直接成本
     */
    private List<SubjectDirectCostInfo> calculateSubjectDirectCost(
            CostAnnualBudgetDetailFullRepository.AnnualBudgetDetailFullInfo annualBudgetDetail,
            Map<String, BigDecimal> procurementPackageAmountMap,
            BigDecimal quarterlyRevenueBudget,
            BigDecimal annualRevenueBudget,
            String excludeQuarterlyBudgetId,
            List<CenterIndirectCostInfo> centerIndirectCostList,
            List<NonOperatingIndirectCostInfo> nonOperatingIndirectCostList,
            List<ComprehensiveIndirectCostInfo> comprehensiveIndirectCostList) {

        List<SubjectDirectCostInfo> result = new ArrayList<>();

        if (annualBudgetDetail.getDirectCostList() == null) {
            return result;
        }

        for (CostAnnualBudgetDetailFullRepository.DirectCostItem directCostItem : annualBudgetDetail.getDirectCostList()) {
            SubjectDirectCostInfo info = new SubjectDirectCostInfo();

            info.setBudgetSubjectCode(directCostItem.getSubjectCode());
            info.setBudgetSubjectName(directCostItem.getSubjectName());
            info.setSubjectDescription(directCostItem.getSubjectDescription());

            // 年度支出预算金额（从年度预算中获取）
            BigDecimal annualExpenditureBudgetAmount = directCostItem.getBudgetAmount() != null ?
                directCostItem.getBudgetAmount() : BigDecimal.ZERO;
            info.setAnnualExpenditureBudgetAmount(annualExpenditureBudgetAmount);

            // 年度剩余支出预算金额 = 年度支出预算金额 - 已审批季度预算支出金额
            // 注意：新增时excludeQuarterlyBudgetId为null，编辑时需要排除当前正在编辑的季度预算
            BigDecimal approvedQuarterlyExpenditure = quarterlyBudgetRepository
                .sumApprovedQuarterlyBudgetExpenditureBySubjectCode(
                    annualBudgetDetail.getBudgetId(), directCostItem.getSubjectCode(), excludeQuarterlyBudgetId);
            BigDecimal annualRemainExpendBudget = annualExpenditureBudgetAmount
                .subtract(approvedQuarterlyExpenditure != null ? approvedQuarterlyExpenditure : BigDecimal.ZERO);
            info.setAnnualRemainExpendBudget(annualRemainExpendBudget);

            // 间接费预算参考金额 = 本季度项目收入预算总额/年度预算的年度收入预算总额*年度预算的该科目间接费预算金额
            // 注意：这里应该是该科目对应的年度间接费预算金额，但由于间接费是按照间接成本科目分类的，
            // 所以这里暂时使用直接成本预算金额作为基数进行比例计算
            BigDecimal indirectCostReferenceAmount = BigDecimal.ZERO;
            if (annualRevenueBudget != null && annualRevenueBudget.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = quarterlyRevenueBudget.divide(annualRevenueBudget, 4, RoundingMode.HALF_UP);
                indirectCostReferenceAmount = annualExpenditureBudgetAmount.multiply(ratio);
            }
            info.setIndirectCostReferenceAmount(indirectCostReferenceAmount);

            // 支出预算金额 = 采办包对应科目的金额
            BigDecimal expenditureBudgetAmount = procurementPackageAmountMap.getOrDefault(
                directCostItem.getSubjectCode(), BigDecimal.ZERO);
            info.setExpenditureBudgetAmount(expenditureBudgetAmount);

            // 间接费预算金额 = 本中心间接成本 + 非经营中心间接成本 + 综合管理间接成本
            // 计算该科目对应的间接费预算金额总和
            BigDecimal indirectCostBudgetAmount = calculateIndirectCostBudgetAmountForSubject(
                directCostItem.getSubjectCode(), centerIndirectCostList, nonOperatingIndirectCostList, comprehensiveIndirectCostList);
            info.setIndirectCostBudgetAmount(indirectCostBudgetAmount);

            result.add(info);
        }

        return result;
    }

    /**
     * 计算本中心间接成本
     * 按照本季度的收入量与年收入量的比例分摊年度预算的对应科目预算
     */
    private List<CenterIndirectCostInfo> calculateCenterIndirectCost(
            CostAnnualBudgetDetailFullRepository.AnnualBudgetDetailFullInfo annualBudgetDetail,
            BigDecimal quarterlyRevenueBudget,
            BigDecimal annualRevenueBudget) {

        List<CenterIndirectCostInfo> result = new ArrayList<>();

        if (annualBudgetDetail.getCenterIndirectCostList() == null ||
            annualRevenueBudget == null || annualRevenueBudget.compareTo(BigDecimal.ZERO) <= 0) {
            return result;
        }

        BigDecimal ratio = quarterlyRevenueBudget.divide(annualRevenueBudget, 4, RoundingMode.HALF_UP);

        for (CostAnnualBudgetDetailFullRepository.CenterIndirectCostItem item : annualBudgetDetail.getCenterIndirectCostList()) {
            CenterIndirectCostInfo info = new CenterIndirectCostInfo();

            info.setBudgetSubjectCode(item.getSubjectCode());
            info.setBudgetSubjectName(item.getSubjectName());
            info.setSubjectDescription(item.getSubjectDescription());

            // 按比例分摊年度预算金额
            BigDecimal expenditureBudgetAmount = item.getBudgetAmount() != null ?
                item.getBudgetAmount().multiply(ratio) : BigDecimal.ZERO;
            info.setExpenditureBudgetAmount(expenditureBudgetAmount);

            result.add(info);
        }

        return result;
    }

    /**
     * 计算非经营中心间接成本
     * 按照本季度的收入量与年收入量的比例分摊年度预算的对应科目预算
     */
    private List<NonOperatingIndirectCostInfo> calculateNonOperatingIndirectCost(
            CostAnnualBudgetDetailFullRepository.AnnualBudgetDetailFullInfo annualBudgetDetail,
            BigDecimal quarterlyRevenueBudget,
            BigDecimal annualRevenueBudget) {

        List<NonOperatingIndirectCostInfo> result = new ArrayList<>();

        if (annualBudgetDetail.getNonOperatingIndirectCostList() == null ||
            annualRevenueBudget == null || annualRevenueBudget.compareTo(BigDecimal.ZERO) <= 0) {
            return result;
        }

        BigDecimal ratio = quarterlyRevenueBudget.divide(annualRevenueBudget, 4, RoundingMode.HALF_UP);

        for (CostAnnualBudgetDetailFullRepository.NonOperatingIndirectCostItem item : annualBudgetDetail.getNonOperatingIndirectCostList()) {
            NonOperatingIndirectCostInfo info = new NonOperatingIndirectCostInfo();

            info.setBudgetSubjectCode(item.getSubjectCode());
            info.setBudgetSubjectName(item.getSubjectName());
            info.setSubjectDescription(item.getSubjectDescription());

            // 按比例分摊年度预算金额
            BigDecimal expenditureBudgetAmount = item.getBudgetAmount() != null ?
                item.getBudgetAmount().multiply(ratio) : BigDecimal.ZERO;
            info.setExpenditureBudgetAmount(expenditureBudgetAmount);

            result.add(info);
        }

        return result;
    }

    /**
     * 计算综合管理间接成本
     * 按照本季度的收入量与年收入量的比例分摊年度预算的对应科目预算
     */
    private List<ComprehensiveIndirectCostInfo> calculateComprehensiveIndirectCost(
            CostAnnualBudgetDetailFullRepository.AnnualBudgetDetailFullInfo annualBudgetDetail,
            BigDecimal quarterlyRevenueBudget,
            BigDecimal annualRevenueBudget) {

        List<ComprehensiveIndirectCostInfo> result = new ArrayList<>();

        if (annualBudgetDetail.getComprehensiveIndirectCostList() == null ||
            annualRevenueBudget == null || annualRevenueBudget.compareTo(BigDecimal.ZERO) <= 0) {
            return result;
        }

        BigDecimal ratio = quarterlyRevenueBudget.divide(annualRevenueBudget, 4, RoundingMode.HALF_UP);

        for (CostAnnualBudgetDetailFullRepository.ComprehensiveIndirectCostItem item : annualBudgetDetail.getComprehensiveIndirectCostList()) {
            ComprehensiveIndirectCostInfo info = new ComprehensiveIndirectCostInfo();

            info.setBudgetSubjectCode(item.getSubjectCode());
            info.setBudgetSubjectName(item.getSubjectName());
            info.setSubjectDescription(item.getSubjectDescription());

            // 按比例分摊年度预算金额
            BigDecimal expenditureBudgetAmount = item.getBudgetAmount() != null ?
                item.getBudgetAmount().multiply(ratio) : BigDecimal.ZERO;
            info.setExpenditureBudgetAmount(expenditureBudgetAmount);

            result.add(info);
        }

        return result;
    }

    /**
     * 计算年度支出剩余预算金额
     * 年度支出剩余预算金额（不含税，元）= 预算科目明细中直接成本处的年度剩余支出预算金额（不含税，元）之和
     */
    private BigDecimal calculateAnnualExpenditureRemainingBudget(List<SubjectDirectCostInfo> subjectDirectCostList) {
        return subjectDirectCostList.stream()
            .map(SubjectDirectCostInfo::getAnnualRemainExpendBudget)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算项目支出预算总额
     * 项目支出预算总额（不含税，元）= 预算科目明细中直接成本处的支出预算金额（不含税，元）之和
     */
    private BigDecimal calculateProjectExpenditureBudgetTotal(List<SubjectDirectCostInfo> subjectDirectCostList) {
        return subjectDirectCostList.stream()
            .map(SubjectDirectCostInfo::getExpenditureBudgetAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算间接费预算总额
     * 间接费预算总额（不含税，元）= 预算科目明细中直接成本处的间接费预算金额（不含税，元）之和
     */
    private BigDecimal calculateIndirectCostBudgetTotal(List<SubjectDirectCostInfo> subjectDirectCostList) {
        return subjectDirectCostList.stream()
            .map(SubjectDirectCostInfo::getIndirectCostBudgetAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算指定科目对应的间接费预算金额
     * 间接费预算金额 = 本中心间接成本 + 非经营中心间接成本 + 综合管理间接成本
     *
     * 注意：由于间接成本是按照间接成本科目分类的，而不是按直接成本科目分类的，
     * 这里的逻辑可能需要根据实际业务需求进行调整。
     * 目前的实现是查找同名科目的间接成本金额进行汇总。
     */
    private BigDecimal calculateIndirectCostBudgetAmountForSubject(
            String subjectCode,
            List<CenterIndirectCostInfo> centerIndirectCostList,
            List<NonOperatingIndirectCostInfo> nonOperatingIndirectCostList,
            List<ComprehensiveIndirectCostInfo> comprehensiveIndirectCostList) {

        BigDecimal totalIndirectCost = BigDecimal.ZERO;

        // 汇总本中心间接成本中同名科目的金额
        if (centerIndirectCostList != null) {
            BigDecimal centerAmount = centerIndirectCostList.stream()
                .filter(item -> subjectCode.equals(item.getBudgetSubjectCode()))
                .map(CenterIndirectCostInfo::getExpenditureBudgetAmount)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalIndirectCost = totalIndirectCost.add(centerAmount);
        }

        // 汇总非经营中心间接成本中同名科目的金额
        if (nonOperatingIndirectCostList != null) {
            BigDecimal nonOperatingAmount = nonOperatingIndirectCostList.stream()
                .filter(item -> subjectCode.equals(item.getBudgetSubjectCode()))
                .map(NonOperatingIndirectCostInfo::getExpenditureBudgetAmount)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalIndirectCost = totalIndirectCost.add(nonOperatingAmount);
        }

        // 汇总综合管理间接成本中同名科目的金额
        if (comprehensiveIndirectCostList != null) {
            BigDecimal comprehensiveAmount = comprehensiveIndirectCostList.stream()
                .filter(item -> subjectCode.equals(item.getBudgetSubjectCode()))
                .map(ComprehensiveIndirectCostInfo::getExpenditureBudgetAmount)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalIndirectCost = totalIndirectCost.add(comprehensiveAmount);
        }

        return totalIndirectCost;
    }

    /**
     * 获取或计算本中心间接成本
     * 优先使用用户输入的数据，如果没有则按比例分摊年度预算
     */
    private List<CenterIndirectCostInfo> getOrCalculateCenterIndirectCost(
            RealTimeCalculationRequest request,
            CostAnnualBudgetDetailFullRepository.AnnualBudgetDetailFullInfo annualBudgetDetail,
            BigDecimal quarterlyRevenueBudget,
            BigDecimal annualRevenueBudget) {

        // 优先使用用户输入的数据
        if (request.getCenterIndirectCostDetails() != null && !request.getCenterIndirectCostDetails().isEmpty()) {
            log.info("使用用户输入的本中心间接成本数据，数量: {}", request.getCenterIndirectCostDetails().size());
            return new ArrayList<>(request.getCenterIndirectCostDetails());
        }

        // 否则按比例分摊年度预算
        log.info("使用年度预算按比例分摊计算本中心间接成本");
        return calculateCenterIndirectCost(annualBudgetDetail, quarterlyRevenueBudget, annualRevenueBudget);
    }

    /**
     * 获取或计算非经营中心间接成本
     * 优先使用用户输入的数据，如果没有则按比例分摊年度预算
     */
    private List<NonOperatingIndirectCostInfo> getOrCalculateNonOperatingIndirectCost(
            RealTimeCalculationRequest request,
            CostAnnualBudgetDetailFullRepository.AnnualBudgetDetailFullInfo annualBudgetDetail,
            BigDecimal quarterlyRevenueBudget,
            BigDecimal annualRevenueBudget) {

        // 优先使用用户输入的数据
        if (request.getNonOperatingIndirectCostDetails() != null && !request.getNonOperatingIndirectCostDetails().isEmpty()) {
            log.info("使用用户输入的非经营中心间接成本数据，数量: {}", request.getNonOperatingIndirectCostDetails().size());
            return new ArrayList<>(request.getNonOperatingIndirectCostDetails());
        }

        // 否则按比例分摊年度预算
        log.info("使用年度预算按比例分摊计算非经营中心间接成本");
        return calculateNonOperatingIndirectCost(annualBudgetDetail, quarterlyRevenueBudget, annualRevenueBudget);
    }

    /**
     * 获取或计算综合管理间接成本
     * 优先使用用户输入的数据，如果没有则按比例分摊年度预算
     */
    private List<ComprehensiveIndirectCostInfo> getOrCalculateComprehensiveIndirectCost(
            RealTimeCalculationRequest request,
            CostAnnualBudgetDetailFullRepository.AnnualBudgetDetailFullInfo annualBudgetDetail,
            BigDecimal quarterlyRevenueBudget,
            BigDecimal annualRevenueBudget) {

        // 优先使用用户输入的数据
        if (request.getComprehensiveIndirectCostDetails() != null && !request.getComprehensiveIndirectCostDetails().isEmpty()) {
            log.info("使用用户输入的综合管理间接成本数据，数量: {}", request.getComprehensiveIndirectCostDetails().size());
            return new ArrayList<>(request.getComprehensiveIndirectCostDetails());
        }

        // 否则按比例分摊年度预算
        log.info("使用年度预算按比例分摊计算综合管理间接成本");
        return calculateComprehensiveIndirectCost(annualBudgetDetail, quarterlyRevenueBudget, annualRevenueBudget);
    }
}
