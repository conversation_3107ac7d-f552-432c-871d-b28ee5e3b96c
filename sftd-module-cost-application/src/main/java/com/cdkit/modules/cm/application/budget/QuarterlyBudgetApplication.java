package com.cdkit.modules.cm.application.budget;

import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetRepository;
import com.cdkit.modules.cm.domain.budget.service.AnnualRevenueRemainingBudgetCalculationService;
import com.cdkit.modules.cm.domain.budget.service.ProcurementPackageQueryService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetNumberGenerationService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetSaveService;
import com.cdkit.modules.cm.domain.budget.service.QuarterTimeCalculationService;
import com.cdkit.modules.cm.domain.budget.service.RevenueDetailQueryService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetRealTimeCalculationService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetIndirectCostImportService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetDetailQueryService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetSubmitService;
import com.cdkit.modules.cm.domain.budget.service.SubjectDirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.CenterIndirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.ComprehensiveIndirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.NonOperatingIndirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.RealTimeCalculationRequest;
import com.cdkit.modules.cm.domain.budget.service.RealTimeCalculationResult;
import com.cdkit.modules.cm.domain.budget.service.ProcurementPackageDetail;
import com.cdkit.modules.cm.domain.budget.service.CenterIndirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.NonOperatingIndirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.ComprehensiveIndirectCostInfo;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetRealTimeCalculationRequestDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetRealTimeCalculationResponseDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetIndirectCostImportRequestDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetIndirectCostImportResponseDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetIndirectCostImportExcelDTO;
import org.springframework.web.multipart.MultipartFile;
import com.cdkitframework.poi.excel.ExcelImportUtil;
import com.cdkitframework.poi.excel.entity.ImportParams;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.InputStream;
import java.math.BigDecimal;
import com.cdkit.modules.cm.domain.project.mode.entity.CostMaterialDetailEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanDetailEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 季度预算应用服务
 * 处理季度预算相关的业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetApplication {

    private final CostQuarterlyBudgetRepository costQuarterlyBudgetRepository;
    private final QuarterlyBudgetSaveService quarterlyBudgetSaveService;
    private final QuarterlyBudgetNumberGenerationService quarterlyBudgetNumberGenerationService;
    private final QuarterTimeCalculationService quarterTimeCalculationService;
    private final ProcurementPackageQueryService procurementPackageQueryService;
    private final CostProjectPlanRepository costProjectPlanRepository;
    private final RevenueDetailQueryService revenueDetailQueryService;
    private final AnnualRevenueRemainingBudgetCalculationService annualRevenueRemainingBudgetCalculationService;
    private final QuarterlyBudgetRealTimeCalculationService quarterlyBudgetRealTimeCalculationService;
    private final QuarterlyBudgetIndirectCostImportService quarterlyBudgetIndirectCostImportService;
    private final QuarterlyBudgetDetailQueryService quarterlyBudgetDetailQueryService;
    private final QuarterlyBudgetSubmitService quarterlyBudgetSubmitService;

    /**
     * 分页查询季度预算列表
     *
     * @param queryEntity 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public PageRes<CostQuarterlyBudgetEntity> queryPageList(CostQuarterlyBudgetEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");
        pageReq.setOrderParam(Arrays.asList(createTimeParam));

        return costQuarterlyBudgetRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 根据ID查询季度预算详情
     *
     * @param id 季度预算ID
     * @return 季度预算实体
     */
    public CostQuarterlyBudgetEntity queryById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("季度预算ID不能为空");
        }

        CostQuarterlyBudgetEntity entity = costQuarterlyBudgetRepository.findById(id);
        if (entity == null) {
            throw new IllegalArgumentException("季度预算不存在，ID: " + id);
        }

        log.info("查询季度预算详情成功，ID: {}, 预算单号: {}", id, entity.getQuarterlyBudgetNo());
        return entity;
    }

    /**
     * 新增季度预算
     *
     * @param entity 季度预算实体
     * @return 季度预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(CostQuarterlyBudgetEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("季度预算数据不能为空");
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证季度预算单号唯一性
        if (StringUtils.hasText(entity.getQuarterlyBudgetNo())) {
            CostQuarterlyBudgetEntity existingEntity = costQuarterlyBudgetRepository.findByQuarterlyBudgetNo(entity.getQuarterlyBudgetNo());
            if (existingEntity != null) {
                throw new IllegalArgumentException("季度预算单号已存在：" + entity.getQuarterlyBudgetNo());
            }
        }

        // 使用领域服务保存主表和所有子表数据
        String savedId = quarterlyBudgetSaveService.saveMainWithDetails(entity);
        log.info("新增季度预算成功，ID: {}, 预算单号: {}", savedId, entity.getQuarterlyBudgetNo());

        return savedId;
    }

    /**
     * 编辑季度预算
     *
     * @param entity 季度预算实体
     * @return 季度预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String edit(CostQuarterlyBudgetEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("季度预算数据不能为空");
        }

        if (!StringUtils.hasText(entity.getId())) {
            throw new IllegalArgumentException("季度预算ID不能为空");
        }

        // 验证季度预算是否存在
        CostQuarterlyBudgetEntity existingEntity = costQuarterlyBudgetRepository.findById(entity.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("季度预算不存在，ID: " + entity.getId());
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证季度预算单号唯一性（排除自身）
        if (StringUtils.hasText(entity.getQuarterlyBudgetNo())) {
            CostQuarterlyBudgetEntity duplicateEntity = costQuarterlyBudgetRepository.findByQuarterlyBudgetNo(entity.getQuarterlyBudgetNo());
            if (duplicateEntity != null && !duplicateEntity.getId().equals(entity.getId())) {
                throw new IllegalArgumentException("季度预算单号已存在：" + entity.getQuarterlyBudgetNo());
            }
        }

        // 使用领域服务更新主表和所有子表数据
        String updatedId = quarterlyBudgetSaveService.updateMainWithDetails(entity);
        log.info("编辑季度预算成功，ID: {}, 预算单号: {}", updatedId, entity.getQuarterlyBudgetNo());

        return updatedId;
    }

    /**
     * 根据ID删除季度预算
     *
     * @param id 季度预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("季度预算ID不能为空");
        }

        // 验证季度预算是否存在
        CostQuarterlyBudgetEntity existingEntity = costQuarterlyBudgetRepository.findById(id);
        if (existingEntity == null) {
            throw new IllegalArgumentException("季度预算不存在，ID: " + id);
        }

        // 通过基础设施层服务删除主表和所有子表数据
        quarterlyBudgetSaveService.deleteMainWithDetails(id);
        log.info("删除季度预算成功，ID: {}, 预算单号: {}", id, existingEntity.getQuarterlyBudgetNo());
    }

    /**
     * 批量删除季度预算
     *
     * @param ids 季度预算ID列表，逗号分隔
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(String ids) {
        if (!StringUtils.hasText(ids)) {
            throw new IllegalArgumentException("季度预算ID列表不能为空");
        }

        List<String> idList = Arrays.asList(ids.split(","));

        // 验证所有ID对应的季度预算是否存在
        List<CostQuarterlyBudgetEntity> existingEntities = costQuarterlyBudgetRepository.findByIds(idList);
        if (existingEntities.size() != idList.size()) {
            throw new IllegalArgumentException("部分季度预算不存在，请检查ID列表");
        }

        // 通过领域服务批量删除主表和所有子表数据
        quarterlyBudgetSaveService.deleteBatchMainWithDetails(idList);
        log.info("批量删除季度预算成功，删除数量: {}, IDs: {}", idList.size(), ids);
    }

    /**
     * 生成下一个季度预算编号
     *
     * @return 下一个季度预算编号（JDYS+8位日期+3位流水）
     */
    public String generateNextQuarterlyBudgetNo() {
        log.info("开始生成下一个季度预算编号");

        String nextBudgetNo = quarterlyBudgetNumberGenerationService.generateNextQuarterlyBudgetNo();

        log.info("生成下一个季度预算编号成功，编号: {}", nextBudgetNo);
        return nextBudgetNo;
    }

    /**
     * 获取季度下拉框选项
     * 返回当年四个季度和下一年第一季度的选项列表（共5个选项）
     *
     * @return 季度选项列表
     */
    public List<String> getQuarterOptions() {
        log.info("开始获取季度下拉框选项");

        List<String> quarterOptions = quarterTimeCalculationService.getQuarterOptions();

        log.info("获取季度下拉框选项成功，共{}个选项", quarterOptions.size());
        return quarterOptions;
    }

    /**
     * 根据选择季度计算开始结束时间
     *
     * @param quarter 季度标识（如"2025年第一季度"）
     * @return 该季度的开始日期和结束日期
     */
    public QuarterTimeCalculationService.QuarterDateRange getQuarterDateRange(String quarter) {
        log.info("开始计算季度日期范围，季度标识: {}", quarter);

        if (!StringUtils.hasText(quarter)) {
            throw new IllegalArgumentException("季度标识不能为空");
        }

        QuarterTimeCalculationService.QuarterDateRange dateRange =
                quarterTimeCalculationService.calculateQuarterDateRange(quarter);

        log.info("计算季度日期范围成功，季度: {}, 开始日期: {}, 结束日期: {}",
                quarter, dateRange.getStartDate(), dateRange.getEndDate());
        return dateRange;
    }

    /**
     * 查询采办包预算科目信息
     * 根据季度计划ID查询原材料及主要原料的预算科目信息
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 采办包预算科目信息列表
     */
    public List<ProcurementPackageQueryService.ProcurementPackageSubjectInfo> queryProcurementPackageSubjects(String quarterlyPlanId) {
        log.info("开始查询采办包预算科目信息，季度计划ID: {}", quarterlyPlanId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyPlanId)) {
                throw new IllegalArgumentException("季度计划ID不能为空");
            }

            // 调用领域服务查询
            List<ProcurementPackageQueryService.ProcurementPackageSubjectInfo> subjectInfoList =
                procurementPackageQueryService.queryProcurementPackageSubjects(quarterlyPlanId);

            log.info("查询采办包预算科目信息成功，季度计划ID: {}, 查询到{}条记录",
                    quarterlyPlanId, subjectInfoList.size());

            return subjectInfoList;

        } catch (Exception e) {
            log.error("查询采办包预算科目信息失败，季度计划ID: {}", quarterlyPlanId, e);
            throw new RuntimeException("查询采办包预算科目信息失败：" + e.getMessage());
        }
    }

    // ==================== 详情查询方法 ====================

    /**
     * 根据季度预算ID查询采办包明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 采办包明细列表
     */
    public List<QuarterlyBudgetDetailQueryService.ProcPkgDetailInfo> queryProcPkgDetailByMainId(String quarterlyBudgetId) {
        return quarterlyBudgetDetailQueryService.queryProcPkgDetailByMainId(quarterlyBudgetId);
    }

    /**
     * 根据季度预算ID查询原材料明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 原材料明细列表
     */
    public List<QuarterlyBudgetDetailQueryService.MaterialDetailInfo> queryMaterialDetailByMainId(String quarterlyBudgetId) {
        return quarterlyBudgetDetailQueryService.queryMaterialDetailByMainId(quarterlyBudgetId);
    }

    /**
     * 根据季度预算ID查询预算科目明细直接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 预算科目明细直接成本列表
     */
    public List<QuarterlyBudgetDetailQueryService.SubjectDirectCostInfo> querySubjectDirectCostByMainId(String quarterlyBudgetId) {
        return quarterlyBudgetDetailQueryService.querySubjectDirectCostByMainId(quarterlyBudgetId);
    }

    /**
     * 根据季度预算ID查询本中心间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 本中心间接成本列表
     */
    public List<QuarterlyBudgetDetailQueryService.CenterIndirectCostInfo> queryCenterIndirectCostByMainId(String quarterlyBudgetId) {
        return quarterlyBudgetDetailQueryService.queryCenterIndirectCostByMainId(quarterlyBudgetId);
    }

    /**
     * 根据季度预算ID查询综合管理间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 综合管理间接成本列表
     */
    public List<QuarterlyBudgetDetailQueryService.ComprehensiveIndirectCostInfo> queryCompMageIndirectCostByMainId(String quarterlyBudgetId) {
        return quarterlyBudgetDetailQueryService.queryCompMageIndirectCostByMainId(quarterlyBudgetId);
    }

    /**
     * 根据季度预算ID查询非经营中心间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 非经营中心间接成本列表
     */
    public List<QuarterlyBudgetDetailQueryService.NonOperatingIndirectCostInfo> queryNonOptCenterIndirectCostByMainId(String quarterlyBudgetId) {
        return quarterlyBudgetDetailQueryService.queryNonOptCenterIndirectCostByMainId(quarterlyBudgetId);
    }

    /**
     * 根据季度预算ID查询收入明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 收入明细列表
     */
    public List<QuarterlyBudgetDetailQueryService.RevenueDetailInfo> queryRevenueDetailByMainId(String quarterlyBudgetId) {
        return quarterlyBudgetDetailQueryService.queryRevenueDetailByMainId(quarterlyBudgetId);
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(CostQuarterlyBudgetEntity entity) {
        if (!StringUtils.hasText(entity.getQuarterlyBudgetName())) {
            throw new IllegalArgumentException("季度预算名称不能为空");
        }

        if (!StringUtils.hasText(entity.getVersion())) {
            throw new IllegalArgumentException("版本不能为空");
        }

        if (!StringUtils.hasText(entity.getBudgetStatus())) {
            throw new IllegalArgumentException("预算状态不能为空");
        }
    }

    /**
     * 根据季度计划ID查询原材料明细
     * 通过季度计划ID查询关联的项目计划的原材料明细数据
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 原材料明细列表
     */
    public List<CostMaterialDetailEntity> queryMaterialDetailByQuarterlyPlanId(String quarterlyPlanId) {
        if (!StringUtils.hasText(quarterlyPlanId)) {
            throw new IllegalArgumentException("季度计划ID不能为空");
        }

        log.info("开始根据季度计划ID查询原材料明细，quarterlyPlanId: {}", quarterlyPlanId);

        try {
            // 调用项目计划仓储查询原材料明细
            List<CostMaterialDetailEntity> materialDetailList = costProjectPlanRepository.queryMaterialDetailByPlanId(quarterlyPlanId);

            if (materialDetailList == null) {
                materialDetailList = new ArrayList<>();
            }

            log.info("根据季度计划ID查询原材料明细成功，quarterlyPlanId: {}, 查询到 {} 条记录",
                    quarterlyPlanId, materialDetailList.size());

            return materialDetailList;

        } catch (Exception e) {
            log.error("根据季度计划ID查询原材料明细失败，quarterlyPlanId: {}", quarterlyPlanId, e);
            throw new RuntimeException("查询原材料明细失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据季度计划ID查询收入明细
     * 通过季度计划ID查询关联的项目计划明细数据，转换为收入明细格式返回
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 收入明细列表
     */
    public List<RevenueDetailQueryService.RevenueDetailInfo> queryRevenueDetailByQuarterlyPlanId(String quarterlyPlanId) {
        if (!StringUtils.hasText(quarterlyPlanId)) {
            throw new IllegalArgumentException("季度计划ID不能为空");
        }

        log.info("开始根据季度计划ID查询收入明细，quarterlyPlanId: {}", quarterlyPlanId);

        try {
            // 调用领域服务查询收入明细数据
            List<RevenueDetailQueryService.RevenueDetailInfo> revenueDetailList =
                revenueDetailQueryService.queryRevenueDetailByQuarterlyPlanId(quarterlyPlanId);

            if (revenueDetailList == null) {
                revenueDetailList = new ArrayList<>();
            }

            log.info("根据季度计划ID查询收入明细成功，quarterlyPlanId: {}, 查询到 {} 条记录",
                    quarterlyPlanId, revenueDetailList.size());

            return revenueDetailList;

        } catch (Exception e) {
            log.error("根据季度计划ID查询收入明细失败，quarterlyPlanId: {}", quarterlyPlanId, e);
            throw new RuntimeException("查询收入明细失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据季度计划ID查询年度收入剩余预算金额
     * 计算公式：年度收入剩余预算金额 = 年度预算的收入预算金额 - 与该年度预算相关的已审批通过的季度预算的项目预算总额
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 年度收入剩余预算详细信息
     */
    public AnnualRevenueRemainingBudgetCalculationService.AnnualRevenueRemainingBudgetInfo queryAnnualRevenueRemainingBudget(String quarterlyPlanId) {
        if (!StringUtils.hasText(quarterlyPlanId)) {
            throw new IllegalArgumentException("季度计划ID不能为空");
        }

        log.info("开始查询年度收入剩余预算金额，quarterlyPlanId: {}", quarterlyPlanId);

        try {
            // 调用领域服务计算年度收入剩余预算金额
            AnnualRevenueRemainingBudgetCalculationService.AnnualRevenueRemainingBudgetInfo budgetInfo =
                annualRevenueRemainingBudgetCalculationService.calculateAnnualRevenueRemainingBudgetInfo(quarterlyPlanId);

            log.info("查询年度收入剩余预算金额成功，quarterlyPlanId: {}, 剩余预算金额: {}元",
                    quarterlyPlanId, budgetInfo.getAnnualRevenueRemainingBudgetAmount());

            return budgetInfo;

        } catch (Exception e) {
            log.error("查询年度收入剩余预算金额失败，quarterlyPlanId: {}", quarterlyPlanId, e);
            throw e;
        }
    }

    /**
     * 提交季度预算
     * 支持单条提交和批量提交两种场景
     *
     * @param budgetIds 预算ID列表
     * @param submitRemark 提交备注
     * @param forceSubmit 是否强制提交
     * @return 提交结果
     */
    @Transactional(rollbackFor = Exception.class)
    public QuarterlyBudgetSubmitService.SubmitResult submitQuarterlyBudgets(List<String> budgetIds, String submitRemark, Boolean forceSubmit) {
        log.info("开始提交季度预算，预算数量: {}, 是否强制提交: {}", budgetIds.size(), forceSubmit);

        try {
            // 构建提交请求
            QuarterlyBudgetSubmitService.SubmitRequest submitRequest =
                new QuarterlyBudgetSubmitService.SubmitRequest(budgetIds, submitRemark, forceSubmit);

            // 调用领域服务执行提交
            QuarterlyBudgetSubmitService.SubmitResult result = quarterlyBudgetSubmitService.submitQuarterlyBudgets(submitRequest);

            log.info("提交季度预算完成，总数量: {}, 成功: {}, 失败: {}",
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());

            return result;

        } catch (Exception e) {
            log.error("提交季度预算失败，预算数量: {}", budgetIds.size(), e);
            throw e;
        }
    }

    /**
     * 季度预算实时计算
     * 根据输入的采办包变更数据和基础信息，实时计算季度预算的各项金额
     *
     * @param request 实时计算请求参数
     * @return 实时计算结果
     */
    public QuarterlyBudgetRealTimeCalculationResponseDTO realTimeCalculation(QuarterlyBudgetRealTimeCalculationRequestDTO request) {
        log.info("开始执行季度预算实时计算，季度计划ID: {}, 年度预算ID: {}",
                request.getQuarterlyPlanId(), request.getAnnualBudgetId());

        try {
            // 参数校验
            if (!StringUtils.hasText(request.getQuarterlyPlanId())) {
                throw new IllegalArgumentException("季度计划ID不能为空");
            }
            if (!StringUtils.hasText(request.getAnnualBudgetId())) {
                throw new IllegalArgumentException("年度预算ID不能为空");
            }

            // 转换为领域请求对象
            RealTimeCalculationRequest domainRequest = convertToRealTimeCalculationRequest(request);

            // 调用领域服务进行实时计算
            RealTimeCalculationResult domainResult =
                quarterlyBudgetRealTimeCalculationService.calculate(domainRequest);

            // 转换为DTO响应对象
            QuarterlyBudgetRealTimeCalculationResponseDTO response =
                convertToRealTimeCalculationResponseDTO(domainResult);

            log.info("季度预算实时计算完成，季度计划ID: {}, 项目支出预算总额: {}元, 间接费预算总额: {}元",
                    request.getQuarterlyPlanId(),
                    response.getProjectExpenditureBudgetTotal(),
                    response.getIndirectCostBudgetTotal());

            return response;

        } catch (Exception e) {
            log.error("季度预算实时计算失败，季度计划ID: {}, 年度预算ID: {}",
                    request.getQuarterlyPlanId(), request.getAnnualBudgetId(), e);
            throw e;
        }
    }

    /**
     * 转换DTO请求为领域请求对象
     */
    private RealTimeCalculationRequest convertToRealTimeCalculationRequest(QuarterlyBudgetRealTimeCalculationRequestDTO dto) {
        RealTimeCalculationRequest request = new RealTimeCalculationRequest();
        request.setQuarterlyPlanId(dto.getQuarterlyPlanId());
        request.setAnnualBudgetId(dto.getAnnualBudgetId());
        request.setProjectRevenueBudgetTotal(dto.getProjectRevenueBudgetTotal());

        // 转换采办包明细
        if (dto.getProcurementPackageDetails() != null) {
            List<ProcurementPackageDetail> details = dto.getProcurementPackageDetails().stream()
                .map(dtoDetail -> {
                    ProcurementPackageDetail detail = new ProcurementPackageDetail();
                    detail.setBudgetSubjectCode(dtoDetail.getBudgetSubjectCode());
                    detail.setBudgetSubjectName(dtoDetail.getBudgetSubjectName());
                    detail.setAmount(dtoDetail.getAmount());
                    return detail;
                })
                .collect(Collectors.toList());
            request.setProcurementPackageDetails(details);
        }

        // 转换本中心间接成本明细
        if (dto.getCenterIndirectCostDetails() != null) {
            List<CenterIndirectCostInfo> centerDetails = dto.getCenterIndirectCostDetails().stream()
                .map(dtoDetail -> {
                    CenterIndirectCostInfo detail = new CenterIndirectCostInfo();
                    detail.setBudgetSubjectCode(dtoDetail.getBudgetSubjectCode());
                    detail.setBudgetSubjectName(dtoDetail.getBudgetSubjectName());
                    detail.setExpenditureBudgetAmount(dtoDetail.getExpenditureBudgetAmount());
                    return detail;
                })
                .collect(Collectors.toList());
            request.setCenterIndirectCostDetails(centerDetails);
        }

        // 转换非经营中心间接成本明细
        if (dto.getNonOperatingIndirectCostDetails() != null) {
            List<NonOperatingIndirectCostInfo> nonOperatingDetails = dto.getNonOperatingIndirectCostDetails().stream()
                .map(dtoDetail -> {
                    NonOperatingIndirectCostInfo detail = new NonOperatingIndirectCostInfo();
                    detail.setBudgetSubjectCode(dtoDetail.getBudgetSubjectCode());
                    detail.setBudgetSubjectName(dtoDetail.getBudgetSubjectName());
                    detail.setExpenditureBudgetAmount(dtoDetail.getExpenditureBudgetAmount());
                    return detail;
                })
                .collect(Collectors.toList());
            request.setNonOperatingIndirectCostDetails(nonOperatingDetails);
        }

        // 转换综合管理间接成本明细
        if (dto.getComprehensiveIndirectCostDetails() != null) {
            List<ComprehensiveIndirectCostInfo> comprehensiveDetails = dto.getComprehensiveIndirectCostDetails().stream()
                .map(dtoDetail -> {
                    ComprehensiveIndirectCostInfo detail = new ComprehensiveIndirectCostInfo();
                    detail.setBudgetSubjectCode(dtoDetail.getBudgetSubjectCode());
                    detail.setBudgetSubjectName(dtoDetail.getBudgetSubjectName());
                    detail.setExpenditureBudgetAmount(dtoDetail.getExpenditureBudgetAmount());
                    return detail;
                })
                .collect(Collectors.toList());
            request.setComprehensiveIndirectCostDetails(comprehensiveDetails);
        }

        return request;
    }

    /**
     * 转换领域结果为DTO响应对象
     */
    private QuarterlyBudgetRealTimeCalculationResponseDTO convertToRealTimeCalculationResponseDTO(RealTimeCalculationResult result) {
        QuarterlyBudgetRealTimeCalculationResponseDTO response = new QuarterlyBudgetRealTimeCalculationResponseDTO();

        // 基本金额信息
        response.setAnnualRevenueRemainingBudget(result.getAnnualRevenueRemainingBudget());
        response.setAnnualExpenditureRemainingBudget(result.getAnnualExpenditureRemainingBudget());
        response.setProjectExpenditureBudgetTotal(result.getProjectExpenditureBudgetTotal());
        response.setIndirectCostBudgetTotal(result.getIndirectCostBudgetTotal());
        response.setProjectMarginalProfit(result.getProjectMarginalProfit());
        response.setProjectMarginalProfitRate(result.getProjectMarginalProfitRate());
        response.setProjectNetProfit(result.getProjectNetProfit());
        response.setProjectNetProfitRate(result.getProjectNetProfitRate());

        // 转换预算科目明细直接成本列表
        if (result.getSubjectDirectCostList() != null) {
            List<QuarterlyBudgetRealTimeCalculationResponseDTO.SubjectDirectCostDTO> subjectDirectCostList =
                result.getSubjectDirectCostList().stream()
                    .map(info -> {
                        QuarterlyBudgetRealTimeCalculationResponseDTO.SubjectDirectCostDTO dto =
                            new QuarterlyBudgetRealTimeCalculationResponseDTO.SubjectDirectCostDTO();
                        dto.setBudgetSubjectCode(info.getBudgetSubjectCode());
                        dto.setBudgetSubjectName(info.getBudgetSubjectName());
                        dto.setSubjectDescription(info.getSubjectDescription());
                        dto.setAnnualExpenditureBudgetAmount(info.getAnnualExpenditureBudgetAmount());
                        dto.setAnnualRemainExpendBudget(info.getAnnualRemainExpendBudget());
                        dto.setIndirectCostReferenceAmount(info.getIndirectCostReferenceAmount());
                        dto.setIndirectCostBudgetAmount(info.getIndirectCostBudgetAmount());
                        dto.setExpenditureBudgetAmount(info.getExpenditureBudgetAmount());
                        return dto;
                    })
                    .collect(Collectors.toList());
            response.setSubjectDirectCostList(subjectDirectCostList);
        }

        // 转换本中心间接成本列表
        if (result.getCenterIndirectCostList() != null) {
            List<QuarterlyBudgetRealTimeCalculationResponseDTO.CenterIndirectCostDTO> centerIndirectCostList =
                result.getCenterIndirectCostList().stream()
                    .map(info -> {
                        QuarterlyBudgetRealTimeCalculationResponseDTO.CenterIndirectCostDTO dto =
                            new QuarterlyBudgetRealTimeCalculationResponseDTO.CenterIndirectCostDTO();
                        dto.setBudgetSubjectCode(info.getBudgetSubjectCode());
                        dto.setBudgetSubjectName(info.getBudgetSubjectName());
                        dto.setSubjectDescription(info.getSubjectDescription());
                        dto.setExpenditureBudgetAmount(info.getExpenditureBudgetAmount());
                        return dto;
                    })
                    .collect(Collectors.toList());
            response.setCenterIndirectCostList(centerIndirectCostList);
        }

        // 转换非经营中心间接成本列表
        if (result.getNonOperatingIndirectCostList() != null) {
            List<QuarterlyBudgetRealTimeCalculationResponseDTO.NonOperatingIndirectCostDTO> nonOperatingIndirectCostList =
                result.getNonOperatingIndirectCostList().stream()
                    .map(info -> {
                        QuarterlyBudgetRealTimeCalculationResponseDTO.NonOperatingIndirectCostDTO dto =
                            new QuarterlyBudgetRealTimeCalculationResponseDTO.NonOperatingIndirectCostDTO();
                        dto.setBudgetSubjectCode(info.getBudgetSubjectCode());
                        dto.setBudgetSubjectName(info.getBudgetSubjectName());
                        dto.setSubjectDescription(info.getSubjectDescription());
                        dto.setExpenditureBudgetAmount(info.getExpenditureBudgetAmount());
                        return dto;
                    })
                    .collect(Collectors.toList());
            response.setNonOperatingIndirectCostList(nonOperatingIndirectCostList);
        }

        // 转换综合管理间接成本列表
        if (result.getComprehensiveIndirectCostList() != null) {
            List<QuarterlyBudgetRealTimeCalculationResponseDTO.ComprehensiveIndirectCostDTO> comprehensiveIndirectCostList =
                result.getComprehensiveIndirectCostList().stream()
                    .map(info -> {
                        QuarterlyBudgetRealTimeCalculationResponseDTO.ComprehensiveIndirectCostDTO dto =
                            new QuarterlyBudgetRealTimeCalculationResponseDTO.ComprehensiveIndirectCostDTO();
                        dto.setBudgetSubjectCode(info.getBudgetSubjectCode());
                        dto.setBudgetSubjectName(info.getBudgetSubjectName());
                        dto.setSubjectDescription(info.getSubjectDescription());
                        dto.setExpenditureBudgetAmount(info.getExpenditureBudgetAmount());
                        return dto;
                    })
                    .collect(Collectors.toList());
            response.setComprehensiveIndirectCostList(comprehensiveIndirectCostList);
        }

        return response;
    }

    /**
     * 季度预算间接成本导入
     * 支持导入本中心间接成本、非经营中心间接成本、综合管理间接成本
     *
     * @param request 导入请求参数
     * @return 导入结果
     */
    public QuarterlyBudgetIndirectCostImportResponseDTO importIndirectCost(QuarterlyBudgetIndirectCostImportRequestDTO request) {
        log.info("开始执行季度预算间接成本导入，季度计划ID: {}, 间接成本类型: {}",
                request.getQuarterlyPlanId(), request.getIndirectCostType());

        try {
            // 参数校验
            if (!StringUtils.hasText(request.getQuarterlyPlanId())) {
                throw new IllegalArgumentException("季度计划ID不能为空");
            }
            if (!StringUtils.hasText(request.getAnnualBudgetId())) {
                throw new IllegalArgumentException("年度预算ID不能为空");
            }
            if (request.getIndirectCostType() == null) {
                throw new IllegalArgumentException("间接成本类型不能为空");
            }
            if (request.getFile() == null || request.getFile().isEmpty()) {
                throw new IllegalArgumentException("Excel文件不能为空");
            }

            List<QuarterlyBudgetIndirectCostImportExcelDTO> excelDataList = parseExcelFile(request.getFile());

            // 转换为领域请求对象
            QuarterlyBudgetIndirectCostImportService.IndirectCostImportRequest domainRequest =
                convertToIndirectCostImportRequest(request, excelDataList);

            // 调用领域服务进行导入
            QuarterlyBudgetIndirectCostImportService.IndirectCostImportResult domainResult =
                quarterlyBudgetIndirectCostImportService.importIndirectCost(domainRequest);

            // 转换为DTO响应对象
            QuarterlyBudgetIndirectCostImportResponseDTO response =
                convertToIndirectCostImportResponseDTO(domainResult);

            log.info("季度预算间接成本导入完成，季度计划ID: {}, 成功: {}条, 失败: {}条",
                    request.getQuarterlyPlanId(), domainResult.getSuccessCount(), domainResult.getFailureCount());

            return response;

        } catch (Exception e) {
            log.error("季度预算间接成本导入失败，季度计划ID: {}", request.getQuarterlyPlanId(), e);
            throw e;
        }
    }

    /**
     * 解析Excel文件
     * 使用cdkit的ExcelImportUtil解析上传的Excel文件
     */
    private List<QuarterlyBudgetIndirectCostImportExcelDTO> parseExcelFile(MultipartFile file) {
        log.info("开始解析Excel文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

        try {
            // 参数校验
            if (file == null || file.isEmpty()) {
                throw new IllegalArgumentException("Excel文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
                throw new IllegalArgumentException("文件格式不正确，请上传.xlsx或.xls格式的Excel文件");
            }

            // 设置导入参数
            ImportParams params = new ImportParams();
            params.setTitleRows(0);     // 标题行数：0行（没有标题行）
            params.setHeadRows(1);      // 表头行数：1行（第1行是表头）
            params.setNeedSave(false);  // 不需要保存到数据库
            params.setStartSheetIndex(0); // 从第一个工作表开始
            params.setSheetNum(1);      // 只读取一个工作表

            List<QuarterlyBudgetIndirectCostImportExcelDTO> importList = null;

            try {
                // 使用cdkit的ExcelImportUtil解析Excel
                importList = ExcelImportUtil.importExcel(
                        file.getInputStream(),
                        QuarterlyBudgetIndirectCostImportExcelDTO.class,
                        params
                );
            } catch (Exception e) {
                log.warn("使用标准方式解析Excel失败，尝试使用兼容模式: {}", e.getMessage());

                // 尝试使用兼容模式解析
                try {
                    // 重新获取输入流
                    InputStream inputStream = file.getInputStream();
                    importList = parseExcelWithCompatibilityMode(inputStream, fileName);
                } catch (Exception compatibilityException) {
                    log.error("兼容模式解析Excel也失败", compatibilityException);
                    throw new RuntimeException("Excel文件解析失败，请检查文件格式是否正确。建议：1.删除文件中的图片和图表；2.另存为新的Excel文件后重试", compatibilityException);
                }
            }

            if (importList == null || importList.isEmpty()) {
                log.warn("Excel文件中没有有效数据");
                throw new IllegalArgumentException("Excel文件中没有有效数据");
            }

            log.info("成功解析Excel文件，共{}条数据", importList.size());

            // 为每条数据自动填充预算科目编码（与预算科目名称相同）
            for (QuarterlyBudgetIndirectCostImportExcelDTO dto : importList) {
                if (dto.getBudgetSubjectName() != null) {
                    dto.setBudgetSubjectCode(dto.getBudgetSubjectName());
                }
            }

            return importList;

        } catch (IllegalArgumentException e) {
            log.error("Excel文件参数校验失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Excel文件解析失败", e);
            throw new RuntimeException("Excel文件解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 兼容模式解析Excel文件
     * 用于处理包含图片、图表等复杂内容的Excel文件
     */
    private List<QuarterlyBudgetIndirectCostImportExcelDTO> parseExcelWithCompatibilityMode(
            InputStream inputStream, String fileName) throws Exception {

        log.info("使用兼容模式解析Excel文件: {}", fileName);

        List<QuarterlyBudgetIndirectCostImportExcelDTO> resultList = new ArrayList<>();

        try {
            // 使用原生POI解析，避免cdkit可能的兼容性问题
            Workbook workbook = null;

            if (fileName.toLowerCase().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(inputStream);
            } else if (fileName.toLowerCase().endsWith(".xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                throw new IllegalArgumentException("不支持的文件格式");
            }

            Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表

            // 跳过表头行，从第二行开始读取数据
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }

                QuarterlyBudgetIndirectCostImportExcelDTO dto = parseRowToDTO(row, rowIndex + 1);
                if (dto != null) {
                    resultList.add(dto);
                }
            }

            workbook.close();

        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }

        log.info("兼容模式解析完成，共解析{}条数据", resultList.size());
        return resultList;
    }

    /**
     * 解析单行数据为DTO对象
     */
    private QuarterlyBudgetIndirectCostImportExcelDTO parseRowToDTO(Row row, int rowNumber) {
        try {
            // 检查是否为空行
            if (isEmptyRow(row)) {
                return null;
            }

            QuarterlyBudgetIndirectCostImportExcelDTO dto = new QuarterlyBudgetIndirectCostImportExcelDTO();

            // 第1列：间接费预算科目
            Cell cell0 = row.getCell(0);
            if (cell0 != null) {
                String subjectName = getCellStringValue(cell0);
                if (StringUtils.hasText(subjectName)) {
                    dto.setBudgetSubjectName(subjectName.trim());
                    dto.setBudgetSubjectCode(subjectName.trim()); // 编码与名称相同
                }
            }

            // 第2列：科目预算金额（元）
            Cell cell1 = row.getCell(1);
            if (cell1 != null) {
                BigDecimal amount = getCellNumericValue(cell1);
                dto.setExpenditureBudgetAmount(amount);
            }

            // 如果科目名称为空，则跳过这行
            if (!StringUtils.hasText(dto.getBudgetSubjectName())) {
                return null;
            }

            return dto;

        } catch (Exception e) {
            log.warn("解析第{}行数据失败: {}", rowNumber, e.getMessage());
            return null;
        }
    }

    /**
     * 检查是否为空行
     */
    private boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }

        for (int i = 0; i < 2; i++) { // 只检查前两列
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String value = getCellStringValue(cell);
                if (StringUtils.hasText(value)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception e2) {
                        return "";
                    }
                }
            default:
                return "";
        }
    }

    /**
     * 获取单元格数值
     */
    private BigDecimal getCellNumericValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    return BigDecimal.valueOf(cell.getNumericCellValue());
                case STRING:
                    String strValue = cell.getStringCellValue().trim();
                    if (StringUtils.hasText(strValue)) {
                        // 移除可能的逗号分隔符
                        strValue = strValue.replace(",", "");
                        return new BigDecimal(strValue);
                    }
                    return null;
                case FORMULA:
                    try {
                        return BigDecimal.valueOf(cell.getNumericCellValue());
                    } catch (Exception e) {
                        String formulaValue = cell.getStringCellValue().trim();
                        if (StringUtils.hasText(formulaValue)) {
                            formulaValue = formulaValue.replace(",", "");
                            return new BigDecimal(formulaValue);
                        }
                        return null;
                    }
                default:
                    return null;
            }
        } catch (Exception e) {
            log.warn("解析数值失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 转换为领域导入请求对象
     */
    private QuarterlyBudgetIndirectCostImportService.IndirectCostImportRequest convertToIndirectCostImportRequest(
            QuarterlyBudgetIndirectCostImportRequestDTO dto,
            List<QuarterlyBudgetIndirectCostImportExcelDTO> excelDataList) {

        QuarterlyBudgetIndirectCostImportService.IndirectCostImportRequest request =
            new QuarterlyBudgetIndirectCostImportService.IndirectCostImportRequest();

        request.setQuarterlyPlanId(dto.getQuarterlyPlanId());
        request.setAnnualBudgetId(dto.getAnnualBudgetId());

        // 转换间接成本类型
        QuarterlyBudgetIndirectCostImportService.IndirectCostType domainType =
            convertIndirectCostType(dto.getIndirectCostType());
        request.setIndirectCostType(domainType);

        // 转换导入数据
        List<QuarterlyBudgetIndirectCostImportService.IndirectCostImportItem> importDataList =
            excelDataList.stream()
                .map(this::convertToIndirectCostImportItem)
                .collect(Collectors.toList());
        request.setImportDataList(importDataList);

        return request;
    }

    /**
     * 转换间接成本类型
     */
    private QuarterlyBudgetIndirectCostImportService.IndirectCostType convertIndirectCostType(
            QuarterlyBudgetIndirectCostImportRequestDTO.IndirectCostType dtoType) {
        switch (dtoType) {
            case CENTER_INDIRECT:
                return QuarterlyBudgetIndirectCostImportService.IndirectCostType.CENTER_INDIRECT;
            case NON_OPERATING_INDIRECT:
                return QuarterlyBudgetIndirectCostImportService.IndirectCostType.NON_OPERATING_INDIRECT;
            case COMPREHENSIVE_INDIRECT:
                return QuarterlyBudgetIndirectCostImportService.IndirectCostType.COMPREHENSIVE_INDIRECT;
            default:
                throw new IllegalArgumentException("不支持的间接成本类型: " + dtoType);
        }
    }

    /**
     * 转换为导入项
     * 注意：预算科目编码与预算科目名称相同
     */
    private QuarterlyBudgetIndirectCostImportService.IndirectCostImportItem convertToIndirectCostImportItem(
            QuarterlyBudgetIndirectCostImportExcelDTO excelDto) {

        QuarterlyBudgetIndirectCostImportService.IndirectCostImportItem item =
            new QuarterlyBudgetIndirectCostImportService.IndirectCostImportItem();

        // 预算科目编码与预算科目名称相同
        item.setBudgetSubjectCode(excelDto.getBudgetSubjectName());
        item.setBudgetSubjectName(excelDto.getBudgetSubjectName());
        item.setSubjectDescription(excelDto.getSubjectDescription());
        item.setExpenditureBudgetAmount(excelDto.getExpenditureBudgetAmount());
        item.setRemark(excelDto.getRemark());

        return item;
    }

    /**
     * 转换为导入响应DTO
     * 简化版本：只包含导入成功/失败列表，不包含预算汇总信息
     */
    private QuarterlyBudgetIndirectCostImportResponseDTO convertToIndirectCostImportResponseDTO(
            QuarterlyBudgetIndirectCostImportService.IndirectCostImportResult domainResult) {

        QuarterlyBudgetIndirectCostImportResponseDTO response = new QuarterlyBudgetIndirectCostImportResponseDTO();

        // 转换成功列表
        if (domainResult.getSuccessList() != null) {
            List<QuarterlyBudgetIndirectCostImportResponseDTO.ImportSuccessItemDTO> successList =
                domainResult.getSuccessList().stream()
                    .map(this::convertToImportSuccessItemDTO)
                    .collect(Collectors.toList());
            response.setSuccessList(successList);
        }

        // 转换失败列表
        if (domainResult.getFailureList() != null) {
            List<QuarterlyBudgetIndirectCostImportResponseDTO.ImportFailureItemDTO> failureList =
                domainResult.getFailureList().stream()
                    .map(this::convertToImportFailureItemDTO)
                    .collect(Collectors.toList());
            response.setFailureList(failureList);
        }

        // 不再包含预算汇总信息，简化响应数据
        response.setBudgetSummary(null);

        return response;
    }

    /**
     * 转换为成功项DTO
     */
    private QuarterlyBudgetIndirectCostImportResponseDTO.ImportSuccessItemDTO convertToImportSuccessItemDTO(
            QuarterlyBudgetIndirectCostImportService.IndirectCostImportItem item) {

        QuarterlyBudgetIndirectCostImportResponseDTO.ImportSuccessItemDTO dto =
            new QuarterlyBudgetIndirectCostImportResponseDTO.ImportSuccessItemDTO();

        dto.setBudgetSubjectCode(item.getBudgetSubjectCode());
        dto.setBudgetSubjectName(item.getBudgetSubjectName());
        dto.setSubjectDescription(item.getSubjectDescription());
        dto.setExpenditureBudgetAmount(item.getExpenditureBudgetAmount());
        dto.setOperationType("导入"); // 默认为导入操作

        return dto;
    }

    /**
     * 转换为失败项DTO
     */
    private QuarterlyBudgetIndirectCostImportResponseDTO.ImportFailureItemDTO convertToImportFailureItemDTO(
            QuarterlyBudgetIndirectCostImportService.ImportFailureItem item) {

        QuarterlyBudgetIndirectCostImportResponseDTO.ImportFailureItemDTO dto =
            new QuarterlyBudgetIndirectCostImportResponseDTO.ImportFailureItemDTO();

        dto.setRowNumber(item.getRowNumber());
        dto.setErrorMessage(item.getErrorMessage());

        if (item.getOriginalData() != null) {
            dto.setBudgetSubjectCode(item.getOriginalData().getBudgetSubjectCode());
            dto.setBudgetSubjectName(item.getOriginalData().getBudgetSubjectName());
            dto.setExpenditureBudgetAmount(item.getOriginalData().getExpenditureBudgetAmount() != null ?
                item.getOriginalData().getExpenditureBudgetAmount().toString() : "");
        }

        return dto;
    }

    // 移除预算汇总转换方法，简化业务流程

    /**
     * 统计并记录子表数据信息
     *
     * @param entity 季度预算实体
     */
    private void logSubTableStatistics(CostQuarterlyBudgetEntity entity) {
        int totalDetailCount = 0;

        if (entity.getCostQuarterlyBudgetProcPkgDetailList() != null && !entity.getCostQuarterlyBudgetProcPkgDetailList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetProcPkgDetailList().size();
            totalDetailCount += count;
            log.info("采办包明细数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetMaterialDetailList() != null && !entity.getCostQuarterlyBudgetMaterialDetailList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetMaterialDetailList().size();
            totalDetailCount += count;
            log.info("原材料明细数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetSubjectDirectCostList() != null && !entity.getCostQuarterlyBudgetSubjectDirectCostList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetSubjectDirectCostList().size();
            totalDetailCount += count;
            log.info("预算科目直接成本数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetCenterIndirectCostList() != null && !entity.getCostQuarterlyBudgetCenterIndirectCostList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetCenterIndirectCostList().size();
            totalDetailCount += count;
            log.info("本中心间接成本数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetCompMageIndirectCostList() != null && !entity.getCostQuarterlyBudgetCompMageIndirectCostList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetCompMageIndirectCostList().size();
            totalDetailCount += count;
            log.info("综合管理间接成本数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList() != null && !entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList().size();
            totalDetailCount += count;
            log.info("非经营中心间接成本数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetRevenueDetailList() != null && !entity.getCostQuarterlyBudgetRevenueDetailList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetRevenueDetailList().size();
            totalDetailCount += count;
            log.info("收入明细数量: {}", count);
        }

        log.info("季度预算子表明细总数量: {} (注意：当前版本只保存主表，子表保存功能待完善)", totalDetailCount);
    }
}
