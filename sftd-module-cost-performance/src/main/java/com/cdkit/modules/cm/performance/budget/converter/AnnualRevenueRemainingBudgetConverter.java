package com.cdkit.modules.cm.performance.budget.converter;

import com.cdkit.modules.cm.api.budget.dto.AnnualRevenueRemainingBudgetDTO;
import com.cdkit.modules.cm.domain.budget.service.AnnualRevenueRemainingBudgetCalculationService;

/**
 * 年度收入剩余预算转换器
 * 负责领域层和API层之间的数据转换
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
public class AnnualRevenueRemainingBudgetConverter {

    /**
     * 将领域层的年度收入剩余预算信息转换为API层DTO
     *
     * @param budgetInfo 领域层年度收入剩余预算信息
     * @return API层DTO
     */
    public static AnnualRevenueRemainingBudgetDTO toDTO(AnnualRevenueRemainingBudgetCalculationService.AnnualRevenueRemainingBudgetInfo budgetInfo) {
        if (budgetInfo == null) {
            return null;
        }

        AnnualRevenueRemainingBudgetDTO dto = new AnnualRevenueRemainingBudgetDTO();
        dto.setAnnualRevenueRemainingBudgetAmount(budgetInfo.getAnnualRevenueRemainingBudgetAmount());
        dto.setAnnualRevenueBudgetAmount(budgetInfo.getAnnualRevenueBudgetAmount());
        dto.setApprovedQuarterlyBudgetTotal(budgetInfo.getApprovedQuarterlyBudgetTotal());
        dto.setAnnualBudgetCode(budgetInfo.getAnnualBudgetCode());
        dto.setAnnualBudgetName(budgetInfo.getAnnualBudgetName());

        return dto;
    }
}
