package com.cdkit.modules.cm.performance.budget.converter;

import com.cdkit.modules.cm.api.budget.dto.ProjectAnnualBudgetQueryDTO;
import com.cdkit.modules.cm.api.budget.converter.ProjectAnnualBudgetQueryConverter;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailRepository;

import java.util.ArrayList;
import java.util.List;

/**
 * 项目年度预算查询Performance层转换器
 * 负责领域层对象和API层DTO之间的转换
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public class ProjectAnnualBudgetQueryPerformanceConverter {

    /**
     * 将领域层ProjectBudgetInfo转换为API层DTO
     * 
     * @param budgetInfo 领域层预算信息
     * @return API层DTO
     */
    public static ProjectAnnualBudgetQueryDTO toDTO(CostAnnualBudgetDetailRepository.ProjectBudgetInfo budgetInfo) {
        if (budgetInfo == null) {
            return null;
        }

        return ProjectAnnualBudgetQueryConverter.createDTO(
            budgetInfo.getAnnualBudgetId(),
            budgetInfo.getAnnualBudgetCode(),
            budgetInfo.getWbsCode(),
            budgetInfo.getProfessionalCompany(),
            budgetInfo.getCenter(),
            budgetInfo.getProjectName(),
            budgetInfo.getBudgetType(),
            budgetInfo.getAnnualRevenueBudget(),
            budgetInfo.getAnnualExpenditureBudget()
        );
    }

    /**
     * 将领域层ProjectBudgetInfo列表转换为API层DTO列表
     * 
     * @param budgetInfoList 领域层预算信息列表
     * @return API层DTO列表
     */
    public static List<ProjectAnnualBudgetQueryDTO> toDTOList(List<CostAnnualBudgetDetailRepository.ProjectBudgetInfo> budgetInfoList) {
        if (budgetInfoList == null || budgetInfoList.isEmpty()) {
            return new ArrayList<>();
        }

        List<ProjectAnnualBudgetQueryDTO> dtoList = new ArrayList<>();
        for (CostAnnualBudgetDetailRepository.ProjectBudgetInfo budgetInfo : budgetInfoList) {
            ProjectAnnualBudgetQueryDTO dto = toDTO(budgetInfo);
            if (dto != null) {
                dtoList.add(dto);
            }
        }

        return dtoList;
    }
}
