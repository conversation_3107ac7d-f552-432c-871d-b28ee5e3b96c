package com.cdkit.modules.cm.performance.budget.converter;

import com.cdkit.modules.cm.api.budget.dto.CostQuarterlyBudgetRevenueDetailDTO;
import com.cdkit.modules.cm.domain.budget.service.RevenueDetailQueryService;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 季度预算收入明细转换器
 * 负责领域对象与DTO之间的转换
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Component
public class CostQuarterlyBudgetRevenueDetailConverter {

    /**
     * 将收入明细信息列表转换为DTO列表
     * 
     * @param revenueDetailInfoList 收入明细信息列表
     * @return DTO列表
     */
    public static List<CostQuarterlyBudgetRevenueDetailDTO> toDTOList(
            List<RevenueDetailQueryService.RevenueDetailInfo> revenueDetailInfoList) {
        
        if (revenueDetailInfoList == null || revenueDetailInfoList.isEmpty()) {
            return new ArrayList<>();
        }

        return revenueDetailInfoList.stream()
                .map(CostQuarterlyBudgetRevenueDetailConverter::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将收入明细信息转换为DTO
     * 
     * @param revenueDetailInfo 收入明细信息
     * @return DTO
     */
    public static CostQuarterlyBudgetRevenueDetailDTO toDTO(
            RevenueDetailQueryService.RevenueDetailInfo revenueDetailInfo) {
        
        if (revenueDetailInfo == null) {
            return null;
        }

        CostQuarterlyBudgetRevenueDetailDTO dto = new CostQuarterlyBudgetRevenueDetailDTO();

        // 基本信息
        dto.setMaterialCode(revenueDetailInfo.getMaterialCode());
        dto.setMaterialName(revenueDetailInfo.getMaterialName());
        dto.setUnit(revenueDetailInfo.getUnit());
        dto.setContractType(revenueDetailInfo.getContractType());

        // 数量和金额信息
        dto.setProductQuantity(revenueDetailInfo.getProductQuantity());
        dto.setEstimatedAnnualProcessVolume(revenueDetailInfo.getEstimatedAnnualProcessVolume());
        dto.setUnitPrice(revenueDetailInfo.getUnitPrice());
        dto.setAnnualReceivableBudget(revenueDetailInfo.getAnnualReceivableBudget());

        // 其他信息
        dto.setCompilationBasis(revenueDetailInfo.getCompilationBasis());
        dto.setRemark(revenueDetailInfo.getRemark());

        // 系统字段
        dto.setCreateTime(revenueDetailInfo.getCreateTime());
        dto.setCreateBy(revenueDetailInfo.getCreateBy());
        dto.setUpdateTime(revenueDetailInfo.getUpdateTime());
        dto.setUpdateBy(revenueDetailInfo.getUpdateBy());
        dto.setTenantId(revenueDetailInfo.getTenantId());
        dto.setDelFlag(revenueDetailInfo.getDelFlag());
        dto.setSysOrgCode(revenueDetailInfo.getSysOrgCode());

        return dto;
    }

    /**
     * 将DTO转换为收入明细信息
     * 
     * @param dto DTO
     * @return 收入明细信息
     */
    public static RevenueDetailQueryService.RevenueDetailInfo toRevenueDetailInfo(
            CostQuarterlyBudgetRevenueDetailDTO dto) {
        
        if (dto == null) {
            return null;
        }

        RevenueDetailQueryService.RevenueDetailInfo revenueDetailInfo = 
            new RevenueDetailQueryService.RevenueDetailInfo();

        // 基本信息
        revenueDetailInfo.setMaterialCode(dto.getMaterialCode());
        revenueDetailInfo.setMaterialName(dto.getMaterialName());
        revenueDetailInfo.setUnit(dto.getUnit());
        revenueDetailInfo.setContractType(dto.getContractType());

        // 数量和金额信息
        revenueDetailInfo.setProductQuantity(dto.getProductQuantity());
        revenueDetailInfo.setEstimatedAnnualProcessVolume(dto.getEstimatedAnnualProcessVolume());
        revenueDetailInfo.setUnitPrice(dto.getUnitPrice());
        revenueDetailInfo.setAnnualReceivableBudget(dto.getAnnualReceivableBudget());

        // 其他信息
        revenueDetailInfo.setCompilationBasis(dto.getCompilationBasis());
        revenueDetailInfo.setRemark(dto.getRemark());

        // 系统字段
        revenueDetailInfo.setCreateTime(dto.getCreateTime());
        revenueDetailInfo.setCreateBy(dto.getCreateBy());
        revenueDetailInfo.setUpdateTime(dto.getUpdateTime());
        revenueDetailInfo.setUpdateBy(dto.getUpdateBy());
        revenueDetailInfo.setTenantId(dto.getTenantId());
        revenueDetailInfo.setDelFlag(dto.getDelFlag());
        revenueDetailInfo.setSysOrgCode(dto.getSysOrgCode());

        return revenueDetailInfo;
    }

    /**
     * 将DTO列表转换为收入明细信息列表
     * 
     * @param dtoList DTO列表
     * @return 收入明细信息列表
     */
    public static List<RevenueDetailQueryService.RevenueDetailInfo> toRevenueDetailInfoList(
            List<CostQuarterlyBudgetRevenueDetailDTO> dtoList) {
        
        if (dtoList == null || dtoList.isEmpty()) {
            return new ArrayList<>();
        }

        return dtoList.stream()
                .map(CostQuarterlyBudgetRevenueDetailConverter::toRevenueDetailInfo)
                .collect(Collectors.toList());
    }
}
